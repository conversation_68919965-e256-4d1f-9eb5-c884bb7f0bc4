<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="#"?>
<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:dt="urn:schemas-microsoft-com:datatypes" xmlns:ps="http://graphpad.com/prism/Prism.htm" version="1.0">
	<!--
	XML style sheet template for formatting data and info tables from GraphPad Prism 7.0.
	Copyright 1992-2016 GraphPad Software, Inc.
-->
	<xsl:output method="html" version="4.0" omit-xml-declaration="yes"/>
	<GraphPadPrismFile xmlns="http://graphpad.com/prism/Prism.htm" PrismXMLVersion="5.00">
<Created>
<OriginalVersion CreatedByProgram="GraphPad Prism" CreatedByVersion="7.0.0.116" Login="Harvey" DateTime="2016-01-20T18:09:38-08:00"/>
<MostRecentVersion CreatedByProgram="GraphPad Prism" CreatedByVersion="7.0.0.121" Login="Alex" DateTime="2016-01-27T13:40:25+02:00"/>
</Created>
<DefGraphButton ButtonID="351"/>
<InfoSequence>
</InfoSequence>
<TableSequence Selected="1">

<Ref ID="Table0" Selected="1"/>
</TableSequence>
<Table ID="Table0" XFormat="none" YFormat="replicates" Replicates="1" TableType="TwoWay" EVFormat="AsteriskAfterNumber">
<Title>Data 1</Title>
<FloatingNote ID="Sticky0" Auto="0" Color="Yellow" Left="296" Top="261" Width="388" Height="151" ScrWidth="1920" ScrHeight="1080" ScrDPI="96">
<WebLink Flags="0" ToolTip="http://www.graphpad.com/guides/prism/7/user-guide/index.htm?heat_maps.htm" URL="http://www.graphpad.com/guides/prism/7/user-guide/index.htm?heat_maps.htm">
Learn about heat maps in Prism
</WebLink>

<Font Size="10" Color="#000000" Face="Arial">
Each value represents normalized gene expression. Go to the linked graph, and choose a Heat map.  Double click on that graph to fine tune its appearance. 
</Font>
</FloatingNote>

<RowTitlesColumn Width="114">
<Subcolumn>
<d>Heart</d>
<d>Lung</d>
<d>Liver</d>
<d>Brain</d>
<d>Kidney</d>
<d>Intestine</d>
</Subcolumn>
</RowTitlesColumn>
<YColumn Width="91" Decimals="0" Subcolumns="1">
<Title>Gene 1</Title>
<Subcolumn>
<d>112</d>
<d>116</d>
<d>98</d>
<d>96</d>
<d>113</d>
<d>110</d>
</Subcolumn>
</YColumn>
<YColumn Width="91" Decimals="0" Subcolumns="1">
<Title>Gene 2</Title>
<Subcolumn>
<d>88</d>
<d>91</d>
<d>114</d>
<d>86</d>
<d>89</d>
<d>91</d>
</Subcolumn>
</YColumn>
<YColumn Width="91" Decimals="0" Subcolumns="1">
<Title>Gene 3</Title>
<Subcolumn>
<d>111</d>
<d>101</d>
<d>110</d>
<d>91</d>
<d>92</d>
<d>113</d>
</Subcolumn>
</YColumn>
<YColumn Width="91" Decimals="0" Subcolumns="1">
<Title>Gene 4</Title>
<Subcolumn>
<d>85</d>
<d>96</d>
<d>111</d>
<d>84</d>
<d>86</d>
<d>112</d>
</Subcolumn>
</YColumn>
<YColumn Width="91" Decimals="0" Subcolumns="1">
<Title>Gene 5</Title>
<Subcolumn>
<d>85</d>
<d>96</d>
<d>107</d>
<d>78</d>
<d>102</d>
<d>114</d>
</Subcolumn>
</YColumn>
<YColumn Width="91" Decimals="0" Subcolumns="1">
<Title>Gene 6</Title>
<Subcolumn>
<d>98</d>
<d>115</d>
<d>109</d>
<d>93</d>
<d>89</d>
<d>105</d>
</Subcolumn>
</YColumn>
</Table>
</GraphPadPrismFile>
	<xsl:template match="ps:Info">
		<a><xsl:attribute name="name"><xsl:value-of select="@ID"/></xsl:attribute></a>
		<table border="0">
			<tr><td><xsl:apply-templates select="ps:Title"/> </td><td width="20"></td><td><b>Notes</b></td></tr>
			<tr><td valign="top">
					<table BORDER="1" CELLSPACING="0">
						<tr><th>Constant</th><th>Name</th></tr>
						<xsl:for-each select="ps:Constant">
							<tr>
								<td><xsl:apply-templates select="ps:Name"/> </td>
								<td><xsl:apply-templates select="ps:Value"/> </td>
							</tr>
						</xsl:for-each>
					</table>
				</td>
				<td width="20">
				</td>
				<td valign="top">
					<xsl:apply-templates select="ps:Notes"/>
				</td></tr>
		</table>
	</xsl:template>
	<xsl:template match="ps:GraphPadPrismFile">
		<HTML>
			<BODY>
				<xsl:variable name="Chars" select="'ABCDEFGHIJKLMNOPQRSTUVWXYZ'"/>
				<font color="#999999">
					<p align="center">This file can be opened by <a href="http://www.graphpad.com">GraphPad</a> Prism (version 
	<xsl:value-of select="@PrismXMLVersion"/> or later). </p></font><hr/>
         This file contains <xsl:value-of select="count(ps:Table|ps:HugeTable)"/> data tables and 
	<xsl:value-of select="count(ps:Info)+count(ps:Table|ps:HugeTable/ps:Info)"/> info tables:

	<dir>
					<xsl:for-each select="ps:Info">
						<li>
							<a>
								<xsl:attribute name="href">#<xsl:value-of select="@ID"/></xsl:attribute>
								<xsl:apply-templates select="ps:Title"/>
							</a>
						</li>
					</xsl:for-each>
					<xsl:for-each select="ps:Table|ps:HugeTable">
						<li>
							<a>
								<xsl:attribute name="href">#<xsl:value-of select="@ID"/></xsl:attribute>
								<xsl:apply-templates select="ps:Title"/>
							</a>
							<dir>
								<xsl:for-each select="ps:Info">
									<li>
										<a>
											<xsl:attribute name="href">#<xsl:value-of select="@ID"/></xsl:attribute>
											<xsl:apply-templates select="ps:Title"/>
										</a>
									</li>
								</xsl:for-each>
							</dir>
						</li>
					</xsl:for-each>
				</dir>
				<hr/>
				<xsl:apply-templates select="ps:Info"/>
				<br/>
				<hr/>
				<br/>
				<xsl:for-each select="ps:Table|ps:HugeTable">
					<xsl:apply-templates select="ps:Title"/>
					<a><xsl:attribute name="name"><xsl:value-of select="@ID"/></xsl:attribute></a>
					<xsl:variable name="width" select="100 div count (*/ps:Subcolumn)"/>
					<xsl:variable name="xFormat" select="./@XFormat"/>
					<xsl:variable name="yFormat" select="./@YFormat"/>
					<xsl:variable name="nRepl" select="./@replicates"/>
					<xsl:variable name="isXAdvancedColumn" select="boolean(($xFormat='date' or $xFormat='time' or $xFormat='startenddate') and count(ps:XAdvancedColumn) &gt; 0)"/>
					<xsl:variable name="isXAdvancedSubcol" select="boolean(($xFormat='startenddate') or count(ps:XAdvancedColumn/ps:Subcolumn/ps:Title) &gt; 0)"/>
					<xsl:variable name="isXSubcol" select="boolean(($xFormat='error' or count(ps:XColumn/ps:Subcolumn/ps:Title) &gt; 0) and not ($isXAdvancedColumn))"/>
					<xsl:variable name="isYSubcol" select="boolean(($yFormat='replicates'and ./@Replicates&gt;1) or 
						 ($yFormat!='text'and $yFormat!='replicates') or 
						count(ps:YColumn/ps:Subcolumn/ps:Title)&gt;0)"/>
					<xsl:variable name="isYSubColNameCustom" select="boolean($isYSubcol and count(ps:SubColumnTitles)&gt;0)"/>
					<TABLE BORDER="1" CELLSPACING="0">
						<TR>
							<xsl:for-each select="ps:RowTitlesColumn">
								<TD align="center">
									<xsl:attribute name="rowspan">
										<xsl:if test="$isXSubcol or $isXAdvancedSubcol or $isYSubcol">2</xsl:if>
									</xsl:attribute><BR/>
								</TD>
							</xsl:for-each>
							<xsl:if test="not ($isXAdvancedColumn)">
								<xsl:for-each select="ps:XColumn">
									<TD align="center">
										<xsl:attribute name="rowspan">
											<xsl:if test="$isYSubcol and not($isXSubcol)">2</xsl:if>
										</xsl:attribute>
										<xsl:attribute name="colspan"><xsl:value-of select="./@Subcolumns"/></xsl:attribute>
						 
						<xsl:apply-templates select="ps:Title"/>
										<xsl:if test="count(ps:Title)=0 or ps:Title=''">X-Title</xsl:if>				
						 
					</TD>
								</xsl:for-each>
							</xsl:if>
							<xsl:if test="$isXAdvancedColumn">
								<xsl:for-each select="ps:XAdvancedColumn">
									<TD align="center">
										<xsl:attribute name="rowspan">
											<xsl:if test="$isYSubcol and not($isXAdvancedSubcol)">2</xsl:if>
										</xsl:attribute>
										<xsl:attribute name="colspan"><xsl:value-of select="./@Subcolumns"/></xsl:attribute>
						 
						<xsl:apply-templates select="ps:Title"/>
										<xsl:if test="count(ps:Title)=0 or ps:Title=''">X-Title</xsl:if>				
						 
					</TD>
								</xsl:for-each>
							</xsl:if>
							<xsl:for-each select="ps:YColumn">
								<xsl:variable name="DefColName2" select="(position()-1) mod 26"/>
								<xsl:variable name="DefColName1" select="(position()-1-$DefColName2) div 26"/>
								<TD align="center">
									<xsl:attribute name="colspan"><xsl:value-of select="./@Subcolumns"/></xsl:attribute>
									<xsl:attribute name="rowspan">
										<xsl:if test="($isXSubcol or $isXAdvancedSubcol) and not($isYSubcol)">2</xsl:if>
									</xsl:attribute>
					 
					<xsl:apply-templates select="ps:Title"/>
									<xsl:if test="count(ps:Title)=0 or ps:Title=''">
										<xsl:value-of select="'Data Set-'"/>
										<xsl:if test="$DefColName1 &gt; 0">
											<xsl:value-of select="substring($Chars,$DefColName1,1)"/>
										</xsl:if>
										<xsl:value-of select="substring($Chars,$DefColName2+1,1)"/>
									</xsl:if>
					 				
	   			</TD>
							</xsl:for-each>
						</TR>
						<xsl:if test="$isXSubcol or $isXAdvancedSubcol or $isYSubcol">
							<TR>
								<xsl:if test="$isXSubcol">
									<xsl:for-each select="ps:XColumn">
										<xsl:for-each select="ps:Subcolumn">
											<TD align="center"> 
					 
					<xsl:apply-templates select="ps:Title"/>
												<xsl:if test="count(ps:Title)=0 or ps:Title=''">
													<xsl:choose>
														<xsl:when test="position()=1"><B>X</B></xsl:when>
														<xsl:when test="position()=2"><B>Err.Bar</B></xsl:when>
													</xsl:choose>
												</xsl:if>				
					 
				</TD>
										</xsl:for-each>
									</xsl:for-each>
								</xsl:if>
								<xsl:if test="$isXAdvancedSubcol">
									<xsl:for-each select="ps:XAdvancedColumn">
										<xsl:for-each select="ps:Subcolumn">
											<TD align="center"> 
					 
					<xsl:apply-templates select="ps:Title"/>
												<xsl:if test="count(ps:Title)=0 or ps:Title=''">
													<xsl:choose>
														<xsl:when test="position()=1"><B>Starting Date</B></xsl:when>
														<xsl:when test="position()=2"><B>Ending Date</B></xsl:when>
													</xsl:choose>
												</xsl:if>				
					 
				</TD>
										</xsl:for-each>
									</xsl:for-each>
								</xsl:if>
								<xsl:if test="$isYSubcol">
									<xsl:for-each select="ps:YColumn">
										<xsl:variable name="DefColName2" select="(position()-1) mod 26"/>
										<xsl:variable name="DefColName1" select="(position()-1-$DefColName2) div 26"/>
										<xsl:variable name="YColumnNdx" select="position()"/>
										<xsl:for-each select="ps:Subcolumn">
											<TD align="center">
					 
					<xsl:apply-templates select="ps:Title"/>
												<xsl:if test="count(ps:Title)=0 or ps:Title=''">
													<xsl:if test="$yFormat='replicates' or $yFormat='text'">
														<B>
															<xsl:if test="$DefColName1 &gt; 0">
																<xsl:value-of select="substring($Chars,$DefColName1,1)"/>
															</xsl:if>
															<xsl:choose>
																<xsl:when test="$isYSubColNameCustom">
																	<xsl:variable name="subColCustomNamePos" select="position()"/>
																	<xsl:for-each select="../../ps:SubColumnTitles/ps:Subcolumn">
																		<xsl:if test="position()=$subColCustomNamePos">
																			<xsl:choose>
																				<xsl:when test="../../ps:SubColumnTitles/@OwnSet='1'">
																					<xsl:variable name="DItemsCount" select="count(./ps:d)"/>
																					<xsl:choose>
																						<xsl:when test="$DItemsCount&lt;$YColumnNdx or ./ps:d[$YColumnNdx]=''">
																							<xsl:value-of select="substring($Chars,$DefColName2+1,1)"/>:Y<xsl:value-of select="position()"/>
																						</xsl:when>
																						<xsl:otherwise>
																							<xsl:apply-templates select="./ps:d[$YColumnNdx]"/>
																						</xsl:otherwise>
																					</xsl:choose>
																				</xsl:when>
																				<xsl:when test="../../ps:SubColumnTitles/@OwnSet='0'">
																					<xsl:variable name="DItemsCount" select="count(./ps:d)"/>
																					<xsl:choose>
																						<xsl:when test="$DItemsCount&lt;1 or ./ps:d[1]=''">
																							<xsl:value-of select="substring($Chars,$DefColName2+1,1)"/>:Y<xsl:value-of select="position()"/>
																						</xsl:when>
																						<xsl:otherwise>
																							<xsl:apply-templates select="./ps:d[1]"/>
																						</xsl:otherwise>
																					</xsl:choose>
																				</xsl:when>
																				<xsl:otherwise>
																					<xsl:apply-templates select="ps:d"/>
																					<xsl:if test="ps:d=''">
																						<xsl:value-of select="substring($Chars,$DefColName2+1,1)"/>:Y<xsl:value-of select="position()"/>
																					</xsl:if>
																				</xsl:otherwise>
																			</xsl:choose>
																		</xsl:if>
																	</xsl:for-each>
																</xsl:when>
																<xsl:otherwise>
																	<xsl:value-of select="substring($Chars,$DefColName2+1,1)"/>:Y<xsl:value-of select="position()"/>
																</xsl:otherwise>
															</xsl:choose>
														</B>
													</xsl:if>
													<xsl:if test="$yFormat!='replicates' and $yFormat!='text'">
														<xsl:if test="not($yFormat='replicates' or $yFormat='text')">
															<xsl:if test="position()=1"><B>Mean</B></xsl:if>
															<xsl:if test="position()!=1">
																<xsl:choose>
																	<xsl:when test="$yFormat='SD'">
																		<B>SD</B>
																	</xsl:when>
																	<xsl:when test="$yFormat='SE'">
																		<B>SEM</B>
																	</xsl:when>
																	<xsl:when test="$yFormat='CV'">
																		<B>%CV</B>
																	</xsl:when>
																	<xsl:when test="$yFormat='SDN'">
																		<xsl:if test="position()=2"><B>SD</B></xsl:if>
																		<xsl:if test="position()=3"><B>N</B></xsl:if>
																	</xsl:when>
																	<xsl:when test="$yFormat='SEN'">
																		<xsl:if test="position()=2"><B>SEM</B></xsl:if>
																		<xsl:if test="position()=3"><B>N</B></xsl:if>
																	</xsl:when>
																	<xsl:when test="$yFormat='CVN'">
																		<xsl:if test="position()=2"><B>%CV</B></xsl:if>
																		<xsl:if test="position()=3"><B>N</B></xsl:if>
																	</xsl:when>
																	<xsl:when test="$yFormat='low-high'">
																		<xsl:if test="position()=2"><B>+Error</B></xsl:if>
																		<xsl:if test="position()=3"><B>-Error</B></xsl:if>
																	</xsl:when>
																	<xsl:when test="$yFormat='upper-lower-limits'">
																		<xsl:if test="position()=2"><B>UpperLimit</B></xsl:if>
																		<xsl:if test="position()=3"><B>LowerLimit</B></xsl:if>
																	</xsl:when>
																</xsl:choose>
															</xsl:if>
														</xsl:if>
													</xsl:if>
												</xsl:if>
					 				
	   			</TD>
										</xsl:for-each>
									</xsl:for-each>
								</xsl:if>
							</TR>
						</xsl:if>
						<TR>
							<xsl:for-each select="ps:RowTitlesColumn/ps:Subcolumn">
								<td valign="top" nowrap="1">
									<xsl:attribute name="width"><xsl:value-of select="$width"/>%</xsl:attribute>
									<xsl:apply-templates select="ps:d"/>
									<xsl:if test="count(ps:d)=0"><br/></xsl:if>
								</td>
							</xsl:for-each>
							<xsl:if test="not($isXAdvancedColumn)">
								<xsl:for-each select="ps:XColumn/ps:Subcolumn">
									<td valign="top" nowrap="1">
										<xsl:attribute name="width"><xsl:value-of select="$width"/>%</xsl:attribute>
										<xsl:apply-templates select="ps:d"/>
										<xsl:if test="count(ps:d)=0"><br/></xsl:if>
									</td>
								</xsl:for-each>
							</xsl:if>
							<xsl:if test="$isXAdvancedColumn">
								<xsl:for-each select="ps:XAdvancedColumn/ps:Subcolumn">
									<td valign="top" nowrap="1">
										<xsl:attribute name="width"><xsl:value-of select="$width"/>%</xsl:attribute>
										<xsl:apply-templates select="ps:d"/>
										<xsl:if test="count(ps:d)=0"><br/></xsl:if>
									</td>
								</xsl:for-each>
							</xsl:if>
							<xsl:for-each select="ps:YColumn/ps:Subcolumn">
								<td valign="top" nowrap="1">
									<xsl:attribute name="width"><xsl:value-of select="$width"/>%</xsl:attribute>
									<xsl:apply-templates select="ps:d"/>
									<xsl:if test="count(ps:d)=0"><br/></xsl:if>
								</td>
							</xsl:for-each>
						</TR>
					</TABLE>
					<br/>
					<xsl:apply-templates select="ps:Info"/>
					<br/>
					<hr/>
					<br/>
				</xsl:for-each>
			</BODY>
		</HTML>
	</xsl:template>
	<!--<xsl:template match="ps:d">
	<xsl:choose>
		<xsl:when test="@Excluded and string-length(text())" >
		    <font color="#0000ee">
	      		<xsl:apply-templates select="node()"/>*		
		    </font>
		</xsl:when>
	    <xsl:otherwise>
			<xsl:apply-templates select="node()"/>
		</xsl:otherwise>
	</xsl:choose>
	<xsl:if test="position()!=last()"><br/></xsl:if>
</xsl:template>-->
	<xsl:template match="ps:d">
		<xsl:choose>
			<xsl:when test="@Excluded and string-length(text())">
				<xsl:choose>
					<xsl:when test="../../../@EVFormat='AsteriskAfterNumber'">
						<font color="#0000ee">
							<xsl:apply-templates select="node()"/>*
						</font>
					</xsl:when>
					<xsl:when test="../../../@EVFormat='Blank'">
					</xsl:when>
					<xsl:otherwise>
						<xsl:apply-templates select="node()"/>
					</xsl:otherwise>
				</xsl:choose>
			</xsl:when>
			<xsl:otherwise>
				<xsl:apply-templates select="node()"/>
			</xsl:otherwise>
		</xsl:choose>
		<xsl:if test="position()!=last()"><br/></xsl:if>
	</xsl:template>
	<xsl:template match="ps:Title">
		<b><xsl:apply-templates select="node()"/></b>
	</xsl:template>
	<xsl:template match="node()">
		<xsl:copy>
			<xsl:apply-templates select="node()"/>
		</xsl:copy>
	</xsl:template>
	<xsl:template match="ps:Font">
		<font>
			<xsl:if test="@Size">
				<xsl:attribute name="STYLE">font-size:<xsl:value-of select="@Size"/>pt</xsl:attribute>
			</xsl:if>
			<xsl:copy-of select="@*"/>
			<xsl:apply-templates select="node()"/>
		</font>
	</xsl:template>
	<xsl:template match="/">
		<HTML>
			<BODY>
				<xsl:apply-templates select="//ps:GraphPadPrismFile"/>
			</BODY>
		</HTML>
	</xsl:template>
</xsl:stylesheet>
