<?xml version="1.0" encoding="UTF-8"?>
<GraphPadPrismFile xmlns="http://graphpad.com/prism/Prism.htm" PrismXMLVersion="5.00">
<Created>
<OriginalVersion CreatedByProgram="GraphPad Prism" CreatedByVersion="7.0.0.139" Login="Dmytro Loboda" DateTime="2016-02-24T12:10:25+02:00"/>
<MostRecentVersion CreatedByProgram="GraphPad Prism" CreatedByVersion="7.0.0.139" Login="Dmytro Loboda" DateTime="2016-02-24T12:10:32+02:00"/>
</Created>
<InfoSequence>
<Ref ID="Info0" Selected="1"/>
</InfoSequence>
<Info ID="Info0">
<Title>Project info dose response experiment 1</Title>
<Notes>
</Notes>
<Constant><Name>Experiment Date</Name><Value>2004-08-02</Value></Constant>
<Constant><Name>Experiment ID</Name><Value/></Constant>
<Constant><Name>Notebook ID</Name><Value/></Constant>
<Constant><Name>Project</Name><Value/></Constant>
<Constant><Name>Experimenter</Name><Value/></Constant>
<Constant><Name>Protocol</Name><Value/></Constant>
</Info>

<TableSequence>

<Ref ID="Table0" Selected="1"/>
</TableSequence>
<Table ID="Table0" XFormat="numbers" YFormat="replicates" Replicates="3" TableType="XY" EVFormat="AsteriskAfterNumber">
<Title>Dose response data - experiment 1</Title>
<FloatingNote ID="Sticky9" Auto="0" Color="Yellow" Left="991" Top="69" Width="236" Height="87" ScrWidth="2560" ScrHeight="1024" ScrDPI="96">
<WebLink Flags="0" ToolTip="@help:reg_dr_stim_variable.htm" URL="@help:reg_dr_stim_variable.htm">
Click here
</WebLink>

<Font Size="10" Face="Arial">
Read a step-by-step guide to create a dose-response curve.
</Font>
</FloatingNote>

<XColumn Width="114" Subcolumns="1" Decimals="2">
<Title>Agonist</Title>
<Subcolumn>
<d>1e-010</d>
<d>1e-008</d>
<d>3e-008</d>
<d>1e-007</d>
<d>3e-007</d>
<d>0.000001</d>
<d>0.000003</d>
<d>0.00001</d>
<d>0.00003</d>
<d>0.0001</d>
<d>0.0003</d>
</Subcolumn>
</XColumn>
<XAdvancedColumn Version="1" Width="114" Decimals="2" Subcolumns="1">
<Title>Agonist</Title>
<Subcolumn>
<d>1e-010</d>
<d>1e-008</d>
<d>3e-008</d>
<d>1e-007</d>
<d>3e-007</d>
<d>0.000001</d>
<d>0.000003</d>
<d>0.00001</d>
<d>0.00003</d>
<d>0.0001</d>
<d>0.0003</d>
</Subcolumn>
</XAdvancedColumn>
<YColumn Width="306" Decimals="1" Subcolumns="3">
<Title>no inhibitor</Title>
<Subcolumn>
<d>0</d>
<d>1</d>
<d>12</d>
<d>19</d>
<d>28</d>
<d>32</d>
<d>35</d>
<d>39</d>
<d/>
<d>39</d>
</Subcolumn>
<Subcolumn>
<d>0</d>
<d>0</d>
<d>10</d>
<d>18</d>
<d>30</d>
<d>35</d>
<d>41</d>
<d>43</d>
<d/>
<d>43</d>
</Subcolumn>
<Subcolumn>
<d>0</d>
<d>4</d>
<d>15</d>
<d>22</d>
<d>28</d>
<d>35</d>
<d>38</d>
<d>44</d>
<d/>
<d>44</d>
</Subcolumn>
</YColumn>
<YColumn Width="306" Decimals="0" Subcolumns="3">
<Title>inhibitor 1:10</Title>
<Subcolumn>
<d>0</d>
<d/>
<d>1</d>
<d>5</d>
<d>8</d>
<d>17</d>
<d>23</d>
<d>27</d>
<d>30</d>
<d>32</d>
<d>32</d>
</Subcolumn>
<Subcolumn>
<d>0</d>
<d/>
<d>0</d>
<d>3</d>
<d>10</d>
<d>20</d>
<d>28</d>
<d>31</d>
<d>34</d>
<d>37</d>
<d>37</d>
</Subcolumn>
<Subcolumn>
<d>0</d>
<d/>
<d>2</d>
<d>6</d>
<d>12</d>
<d>23</d>
<d>29</d>
<d>34</d>
<d>38</d>
<d>38</d>
<d/>
</Subcolumn>
</YColumn>
</Table>

<!--Analyses, graphs and layouts as compressed binary. Don't edit this part of the file.-->

<Template xmlns:dt="urn:schemas-microsoft-com:datatypes" dt:dt="bin.base64">eNrsfQlgFEW6f3X3JJkkk8nkEALhaE4TSMIkgSSwhAyHmCgiAkpgWWFIJsk8JzNhZsLhxejq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</Template></GraphPadPrismFile>
