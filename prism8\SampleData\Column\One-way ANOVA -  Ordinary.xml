<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="#"?>
<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:dt="urn:schemas-microsoft-com:datatypes" xmlns:ps="http://graphpad.com/prism/Prism.htm" version="1.0">
	<!--
	XML style sheet template for formatting data and info tables from GraphPad Prism 6.0.
	Copyright 1992-2012 GraphPad Software, Inc.
-->
	<xsl:output method="html" version="4.0" omit-xml-declaration="yes"/>
	<GraphPadPrismFile xmlns="http://graphpad.com/prism/Prism.htm" PrismXMLVersion="5.00">
<Created>
<OriginalVersion CreatedByProgram="GraphPad Prism" CreatedByVersion="*******" DateTime="2013-10-02T13:18:47+02:00"/>
<MostRecentVersion CreatedByProgram="GraphPad Prism" CreatedByVersion="*******" DateTime="2013-10-02T16:57:03+02:00"/>
</Created>
<DefGraphButton ButtonID="201"/>
<InfoSequence>
</InfoSequence>
<TableSequence Selected="1">
<Ref ID="Table0" Selected="1"/>
</TableSequence>
<Table ID="Table0" XFormat="none" TableType="OneWay" EVFormat="AsteriskAfterNumber">
<Title>One-way ANOVA data</Title>
<FloatingNote ID="Sticky0" Color="Yellow" Left="203" Top="242" Width="544" Height="359">
<WebLink Flags="0" ToolTip="@help:stat_howto_1wayanova." URL="@help:stat_howto_1wayanova">
Step by step instructions for performing one-way ANOVA
</WebLink>

<B><Font Size="11" Face="Verdana">
How the data are organized
</Font></B>
<Font Size="11" Face="Verdana">
<BR/>The columns define three treatments. Note that, unlike many statistics programs, Prism does not define groups by using a grouping variable. Instead, the groups are defined by the columns. Note that one value is missing. This is fine for ordinary one-way ANOVA (but not for repeated measures).
</Font>
<B><Font Size="11" Face="Verdana">
<BR/><BR/>The goals
</Font></B>
<Font Size="11" Face="Verdana">
<BR/>- To determine if the differences between the group means
</Font>
<Font Size="11" Face="Verdana">
<BR/>   are greater than you'd expect to see by chance.
</Font>
<B><Font Size="11" Face="Verdana">
<BR/>- 
</Font></B>
<Font Size="11" Face="Verdana">
To determine the 95% confidence interval for the difference
</Font>
<Font Size="11" Face="Verdana">
<BR/>  between the pairs of group means (post tests).
</Font>
<B><Font Size="11" Face="Verdana">
<BR/><BR/>How to perform one-way ANOVA
</Font></B>
<Font Size="11" Face="Verdana">
<BR/>Click  Analyze, choose one-way ANOVA from the list of column analyses, and then accept all the default choices
</Font>
<Font Size="11" Face="Verdana">
 on the dialog. Click the link below for detailed instructions, and to learn about one way ANOVA.
</Font>
</FloatingNote>

<YColumn Width="81" Decimals="0" Subcolumns="1">
<Title>Control</Title>
<Subcolumn>
<d>54</d>
<d>23</d>
<d>45</d>
<d>54</d>
<d>45</d>
<d>47</d>
</Subcolumn>
</YColumn>
<YColumn Width="81" Decimals="0" Subcolumns="1">
<Title>Treated</Title>
<Subcolumn>
<d>87</d>
<d>98</d>
<d>64</d>
<d>77</d>
<d>89</d>
</Subcolumn>
</YColumn>
<YColumn Width="117" Decimals="0" Subcolumns="1">
<Title>Treated+Antagonist</Title>
<Subcolumn>
<d>45</d>
<d>39</d>
<d>51</d>
<d>49</d>
<d>50</d>
<d>55</d>
</Subcolumn>
</YColumn>
</Table>
</GraphPadPrismFile>
	<xsl:template match="ps:Info">
		<a><xsl:attribute name="name"><xsl:value-of select="@ID"/></xsl:attribute></a>
		<table border="0">
			<tr><td><xsl:apply-templates select="ps:Title"/> </td><td width="20"></td><td><b>Notes</b></td></tr>
			<tr><td valign="top">
					<table BORDER="1" CELLSPACING="0">
						<tr><th>Constant</th><th>Name</th></tr>
						<xsl:for-each select="ps:Constant">
							<tr>
								<td><xsl:apply-templates select="ps:Name"/> </td>
								<td><xsl:apply-templates select="ps:Value"/> </td>
							</tr>
						</xsl:for-each>
					</table>
				</td>
				<td width="20">
				</td>
				<td valign="top">
					<xsl:apply-templates select="ps:Notes"/>
				</td></tr>
		</table>
	</xsl:template>
	<xsl:template match="ps:GraphPadPrismFile">
		<HTML>
			<BODY>
				<xsl:variable name="Chars" select="'ABCDEFGHIJKLMNOPQRSTUVWXYZ'"/>
				<font color="#999999">
					<p align="center">This file can be opened by <a href="http://www.graphpad.com">GraphPad</a> Prism (version 
	<xsl:value-of select="@PrismXMLVersion"/> or later). </p></font><hr/>
         This file contains <xsl:value-of select="count(ps:Table|ps:HugeTable)"/> data tables and 
	<xsl:value-of select="count(ps:Info)+count(ps:Table|ps:HugeTable/ps:Info)"/> info tables:

	<dir>
					<xsl:for-each select="ps:Info">
						<li>
							<a>
								<xsl:attribute name="href">#<xsl:value-of select="@ID"/></xsl:attribute>
								<xsl:apply-templates select="ps:Title"/>
							</a>
						</li>
					</xsl:for-each>
					<xsl:for-each select="ps:Table|ps:HugeTable">
						<li>
							<a>
								<xsl:attribute name="href">#<xsl:value-of select="@ID"/></xsl:attribute>
								<xsl:apply-templates select="ps:Title"/>
							</a>
							<dir>
								<xsl:for-each select="ps:Info">
									<li>
										<a>
											<xsl:attribute name="href">#<xsl:value-of select="@ID"/></xsl:attribute>
											<xsl:apply-templates select="ps:Title"/>
										</a>
									</li>
								</xsl:for-each>
							</dir>
						</li>
					</xsl:for-each>
				</dir>
				<hr/>
				<xsl:apply-templates select="ps:Info"/>
				<br/>
				<hr/>
				<br/>
				<xsl:for-each select="ps:Table|ps:HugeTable">
					<xsl:apply-templates select="ps:Title"/>
					<a><xsl:attribute name="name"><xsl:value-of select="@ID"/></xsl:attribute></a>
					<xsl:variable name="width" select="100 div count (*/ps:Subcolumn)"/>
					<xsl:variable name="xFormat" select="./@XFormat"/>
					<xsl:variable name="yFormat" select="./@YFormat"/>
					<xsl:variable name="nRepl" select="./@replicates"/>
					<xsl:variable name="isXSubcol" select="boolean($xFormat='error' or count(ps:XColumn/ps:Subcolumn/ps:Title)&gt;0)"/>
					<xsl:variable name="isYSubcol" select="boolean(($yFormat='replicates'and ./@Replicates&gt;1) or 
						 ($yFormat!='text'and $yFormat!='replicates') or 
						count(ps:YColumn/ps:Subcolumn/ps:Title)&gt;0)"/>
					<TABLE BORDER="1" CELLSPACING="0">
						<TR>
							<xsl:for-each select="ps:RowTitlesColumn">
								<TD align="center">
									<xsl:attribute name="rowspan">
										<xsl:if test="$isXSubcol or $isYSubcol">2</xsl:if>
									</xsl:attribute><BR/>
								</TD>
							</xsl:for-each>
							<xsl:for-each select="ps:XColumn">
								<TD align="center">
									<xsl:attribute name="rowspan">
										<xsl:if test="$isYSubcol and not($isXSubcol)">2</xsl:if>
									</xsl:attribute>
									<xsl:attribute name="colspan"><xsl:value-of select="./@Subcolumns"/></xsl:attribute>
					 
					<xsl:apply-templates select="ps:Title"/>
									<xsl:if test="count(ps:Title)=0 or ps:Title=''">X-Title</xsl:if>				
					 
				</TD>
							</xsl:for-each>
							<xsl:for-each select="ps:YColumn">
								<xsl:variable name="DefColName2" select="(position()-1) mod 26"/>
								<xsl:variable name="DefColName1" select="(position()-1-$DefColName2) div 26"/>
								<TD align="center">
									<xsl:attribute name="colspan"><xsl:value-of select="./@Subcolumns"/></xsl:attribute>
									<xsl:attribute name="rowspan">
										<xsl:if test="$isXSubcol and not($isYSubcol)">2</xsl:if>
									</xsl:attribute>
					 
					<xsl:apply-templates select="ps:Title"/>
									<xsl:if test="count(ps:Title)=0 or ps:Title=''">
										<xsl:value-of select="'Data Set-'"/>
										<xsl:if test="$DefColName1 &gt; 0">
											<xsl:value-of select="substring($Chars,$DefColName1,1)"/>
										</xsl:if>
										<xsl:value-of select="substring($Chars,$DefColName2+1,1)"/>
									</xsl:if>
					 				
	   			</TD>
							</xsl:for-each>
						</TR>
						<xsl:if test="$isXSubcol or $isYSubcol">
							<TR>
								<xsl:if test="$isXSubcol">
									<xsl:for-each select="ps:XColumn">
										<xsl:for-each select="ps:Subcolumn">
											<TD align="center"> 
					 
					<xsl:apply-templates select="ps:Title"/>
												<xsl:if test="count(ps:Title)=0 or ps:Title=''">
													<xsl:choose>
														<xsl:when test="position()=1"><B>X</B></xsl:when>
														<xsl:when test="position()=2"><B>Err.Bar</B></xsl:when>
													</xsl:choose>
												</xsl:if>				
					 
				</TD>
										</xsl:for-each>
									</xsl:for-each>
								</xsl:if>
								<xsl:if test="$isYSubcol">
									<xsl:for-each select="ps:YColumn">
										<xsl:variable name="DefColName2" select="(position()-1) mod 26"/>
										<xsl:variable name="DefColName1" select="(position()-1-$DefColName2) div 26"/>
										<xsl:for-each select="ps:Subcolumn">
											<TD align="center">
					 
					<xsl:apply-templates select="ps:Title"/>
												<xsl:if test="count(ps:Title)=0 or ps:Title=''">
													<xsl:if test="$yFormat='replicates' or $yFormat='text'">
														<B>
															<xsl:if test="$DefColName1 &gt; 0">
																<xsl:value-of select="substring($Chars,$DefColName1,1)"/>
															</xsl:if>
															<xsl:value-of select="substring($Chars,$DefColName2+1,1)"/>:Y<xsl:value-of select="position()"/>
														</B>
													</xsl:if>
													<xsl:if test="$yFormat!='replicates' and $yFormat!='text'">
														<xsl:if test="not($yFormat='replicates' or $yFormat='text')">
															<xsl:if test="position()=1"><B>Mean</B></xsl:if>
															<xsl:if test="position()!=1">
																<xsl:choose>
																	<xsl:when test="$yFormat='SD'">
																		<B>SD</B>
																	</xsl:when>
																	<xsl:when test="$yFormat='SE'">
																		<B>SEM</B>
																	</xsl:when>
																	<xsl:when test="$yFormat='CV'">
																		<B>%CV</B>
																	</xsl:when>
																	<xsl:when test="$yFormat='SDN'">
																		<xsl:if test="position()=2"><B>SD</B></xsl:if>
																		<xsl:if test="position()=3"><B>N</B></xsl:if>
																	</xsl:when>
																	<xsl:when test="$yFormat='SEN'">
																		<xsl:if test="position()=2"><B>SEM</B></xsl:if>
																		<xsl:if test="position()=3"><B>N</B></xsl:if>
																	</xsl:when>
																	<xsl:when test="$yFormat='CVN'">
																		<xsl:if test="position()=2"><B>%CV</B></xsl:if>
																		<xsl:if test="position()=3"><B>N</B></xsl:if>
																	</xsl:when>
																	<xsl:when test="$yFormat='low-high'">
																		<xsl:if test="position()=2"><B>+Error</B></xsl:if>
																		<xsl:if test="position()=3"><B>-Error</B></xsl:if>
																	</xsl:when>
																	<xsl:when test="$yFormat='upper-lower-limits'">
																		<xsl:if test="position()=2"><B>UpperLimit</B></xsl:if>
																		<xsl:if test="position()=3"><B>LowerLimit</B></xsl:if>
																	</xsl:when>
																</xsl:choose>
															</xsl:if>
														</xsl:if>
													</xsl:if>
												</xsl:if>
					 				
	   			</TD>
										</xsl:for-each>
									</xsl:for-each>
								</xsl:if>
							</TR>
						</xsl:if>
						<TR>
							<xsl:for-each select="ps:RowTitlesColumn/ps:Subcolumn">
								<td valign="top" nowrap="1">
									<xsl:attribute name="width"><xsl:value-of select="$width"/>%</xsl:attribute>
									<xsl:apply-templates select="ps:d"/>
									<xsl:if test="count(ps:d)=0"><br/></xsl:if>
								</td>
							</xsl:for-each>
							<xsl:for-each select="ps:XColumn/ps:Subcolumn">
								<td valign="top" nowrap="1">
									<xsl:attribute name="width"><xsl:value-of select="$width"/>%</xsl:attribute>
									<xsl:apply-templates select="ps:d"/>
									<xsl:if test="count(ps:d)=0"><br/></xsl:if>
								</td>
							</xsl:for-each>
							<xsl:for-each select="ps:YColumn/ps:Subcolumn">
								<td valign="top" nowrap="1">
									<xsl:attribute name="width"><xsl:value-of select="$width"/>%</xsl:attribute>
									<xsl:apply-templates select="ps:d"/>
									<xsl:if test="count(ps:d)=0"><br/></xsl:if>
								</td>
							</xsl:for-each>
						</TR>
					</TABLE>
					<br/>
					<xsl:apply-templates select="ps:Info"/>
					<br/>
					<hr/>
					<br/>
				</xsl:for-each>
			</BODY>
		</HTML>
	</xsl:template>
	<!--<xsl:template match="ps:d">
	<xsl:choose>
		<xsl:when test="@Excluded and string-length(text())" >
		    <font color="#0000ee">
	      		<xsl:apply-templates select="node()"/>*		
		    </font>
		</xsl:when>
	    <xsl:otherwise>
			<xsl:apply-templates select="node()"/>
		</xsl:otherwise>
	</xsl:choose>
	<xsl:if test="position()!=last()"><br/></xsl:if>
</xsl:template>-->
	<xsl:template match="ps:d">
		<xsl:choose>
			<xsl:when test="@Excluded and string-length(text())">
				<xsl:choose>
					<xsl:when test="../../../@EVFormat='AsteriskAfterNumber'">
						<font color="#0000ee">
							<xsl:apply-templates select="node()"/>*
						</font>
					</xsl:when>
					<xsl:when test="../../../@EVFormat='Blank'">
					</xsl:when>
					<xsl:otherwise>
						<xsl:apply-templates select="node()"/>
					</xsl:otherwise>
				</xsl:choose>
			</xsl:when>
			<xsl:otherwise>
				<xsl:apply-templates select="node()"/>
			</xsl:otherwise>
		</xsl:choose>
		<xsl:if test="position()!=last()"><br/></xsl:if>
	</xsl:template>
	<xsl:template match="ps:Title">
		<b><xsl:apply-templates select="node()"/></b>
	</xsl:template>
	<xsl:template match="node()">
		<xsl:copy>
			<xsl:apply-templates select="node()"/>
		</xsl:copy>
	</xsl:template>
	<xsl:template match="ps:Font">
		<font>
			<xsl:if test="@Size">
				<xsl:attribute name="STYLE">font-size:<xsl:value-of select="@Size"/>pt</xsl:attribute>
			</xsl:if>
			<xsl:copy-of select="@*"/>
			<xsl:apply-templates select="node()"/>
		</font>
	</xsl:template>
	<xsl:template match="/">
		<HTML>
			<BODY>
				<xsl:apply-templates select="//ps:GraphPadPrismFile"/>
			</BODY>
		</HTML>
	</xsl:template>
</xsl:stylesheet>
