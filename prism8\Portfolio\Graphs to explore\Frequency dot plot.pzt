<?xml version="1.0" encoding="UTF-8"?>
<GraphPadPrismFile xmlns="http://graphpad.com/prism/Prism.htm" PrismXMLVersion="5.00">
<Created>
<OriginalVersion CreatedByProgram="GraphPad Prism" CreatedByVersion="7.0.0.139" Login="Dmytro Loboda" DateTime="2016-02-24T11:33:03+02:00"/>
<MostRecentVersion CreatedByProgram="GraphPad Prism" CreatedByVersion="7.0.0.139" Login="Dmytro Loboda" DateTime="2016-02-24T11:36:26+02:00"/>
</Created>
<InfoSequence>
<Ref ID="Info0" Selected="1"/>
</InfoSequence>
<Info ID="Info0">
<Title>Project info 1</Title>
<Notes>
</Notes>
<Constant><Name>Experiment Date</Name><Value>2015-07-27</Value></Constant>
<Constant><Name>Experiment ID</Name><Value/></Constant>
<Constant><Name>Notebook ID</Name><Value/></Constant>
<Constant><Name>Project</Name><Value/></Constant>
<Constant><Name>Experimenter</Name><Value/></Constant>
<Constant><Name>Protocol</Name><Value/></Constant>
</Info>

<TableSequence>

<Ref ID="Table0"/>
<Ref ID="Table3" Selected="1"/>
</TableSequence>
<Table ID="Table0" XFormat="none" TableType="OneWay" EVFormat="AsteriskAfterNumber">
<Title>Source data</Title>
<FloatingNote ID="Sticky4" Auto="0" Color="Yellow" Left="523" Top="101" Width="462" Height="151" ScrWidth="2560" ScrHeight="1024" ScrDPI="96">

<Font Size="10" Color="#000000" Face="Arial">
Create a column format for your data 
</Font>
<I><Font Size="10" Color="#000000" Face="Arial">
(this might be a good time to check for outliers).<BR/><BR/>
</Font></I>
<Font Size="10" Color="#000000" Face="Arial">
Go to column analysis and do a frequence distribution...leave all choices alone for now.
</Font>
</FloatingNote>

<YColumn Width="92" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d>12</d>
<d>8</d>
<d>9</d>
<d>10</d>
<d>8</d>
<d>9</d>
<d>9</d>
<d>12</d>
<d>8</d>
<d>13</d>
<d>10</d>
<d>11</d>
<d>8</d>
<d>12</d>
<d>9</d>
<d>9</d>
<d>7</d>
<d>11</d>
<d>17</d>
<d>5</d>
<d>6</d>
<d>6</d>
<d>9</d>
<d>11</d>
<d>8</d>
<d>12</d>
<d>11</d>
<d>7</d>
<d>12</d>
<d>12</d>
<d>8</d>
<d>9</d>
<d>9</d>
<d>10</d>
<d>8</d>
<d>18</d>
<d>11</d>
<d>10</d>
<d>12</d>
<d>12</d>
<d>9</d>
<d>7</d>
<d>11</d>
<d>8</d>
<d>9</d>
<d>13</d>
<d>11</d>
<d>12</d>
<d>7</d>
<d>11</d>
<d>14</d>
<d>9</d>
<d>7</d>
<d>8</d>
<d>12</d>
<d>5</d>
<d>14</d>
<d>6</d>
<d>6</d>
<d>10</d>
<d>8</d>
<d>10</d>
<d>9</d>
<d>12</d>
<d>9</d>
<d>13</d>
<d>9</d>
<d>15</d>
<d>11</d>
<d>8</d>
<d>14</d>
<d>10</d>
<d>14</d>
<d>13</d>
<d>10</d>
<d>11</d>
<d>8</d>
<d>13</d>
<d>9</d>
<d>9</d>
<d>6</d>
<d>15</d>
<d>10</d>
<d>10</d>
<d>12</d>
<d>8</d>
<d>10</d>
<d>12</d>
<d>9</d>
<d>6</d>
<d>15</d>
<d>7</d>
<d>4</d>
<d>11</d>
<d>10</d>
<d>14</d>
<d>5</d>
<d>6</d>
<d>10</d>
<d>15</d>
</Subcolumn>
</YColumn>
</Table>
<Table ID="Table3" XFormat="numbers" YFormat="replicates" Replicates="1" TableType="XY" EVFormat="AsteriskAfterNumber">
<Title>Data from frequency distribution</Title>
<FloatingNote ID="Sticky5" Auto="0" Color="Yellow" Left="607" Top="484" Width="629" Height="151" ScrWidth="2560" ScrHeight="1024" ScrDPI="96">

<Font Size="10" Color="#000000" Face="Arial">
The X column is for the bin center.<BR/><BR/>For each 
</Font>
<I><Font Size="10" Color="#000000" Face="Arial">
row
</Font></I>
<Font Size="10" Color="#000000" Face="Arial">
 of the data table,  enter a value for each dot you want to see in that 
</Font>
<I><Font Size="10" Color="#000000" Face="Arial">
column
</Font></I>
<Font Size="10" Color="#000000" Face="Arial">
 of the graph. <BR/>For example...row 4 (which also has bin center equal to 4) has 6 values so will show as six symbols in the graph.
</Font>
</FloatingNote>

<XColumn Width="114" Subcolumns="1" Decimals="1">
<Title/>
<Subcolumn>
<d>4</d>
<d>5</d>
<d>6</d>
<d>7</d>
<d>8</d>
<d>9</d>
<d>10</d>
<d>11</d>
<d>12</d>
<d>13</d>
<d>14</d>
<d>15</d>
<d>16</d>
<d>17</d>
<d>18</d>
</Subcolumn>
</XColumn>
<XAdvancedColumn Version="1" Width="114" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d>4</d>
<d>5</d>
<d>6</d>
<d>7</d>
<d>8</d>
<d>9</d>
<d>10</d>
<d>11</d>
<d>12</d>
<d>13</d>
<d>14</d>
<d>15</d>
<d>16</d>
<d>17</d>
<d>18</d>
</Subcolumn>
</XAdvancedColumn>
<YColumn Width="92" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d>1</d>
<d>1</d>
<d>1</d>
<d>1</d>
<d>1</d>
<d>1</d>
<d>1</d>
<d>1</d>
<d>1</d>
<d>1</d>
<d>1</d>
<d>1</d>
<d/>
<d>1</d>
<d>1</d>
</Subcolumn>
</YColumn>
<YColumn Width="92" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d/>
<d>2</d>
<d>2</d>
<d>2</d>
<d>2</d>
<d>2</d>
<d>2</d>
<d>2</d>
<d>2</d>
<d>2</d>
<d>2</d>
<d>2</d>
<d/>
<d>1</d>
<d>1</d>
</Subcolumn>
</YColumn>
<YColumn Width="92" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d/>
<d>3</d>
<d>3</d>
<d>3</d>
<d>3</d>
<d>3</d>
<d>3</d>
<d>3</d>
<d>3</d>
<d>3</d>
<d>3</d>
<d>3</d>
</Subcolumn>
</YColumn>
<YColumn Width="92" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d/>
<d/>
<d>4</d>
<d>4</d>
<d>4</d>
<d>4</d>
<d>4</d>
<d>4</d>
<d>4</d>
<d>4</d>
<d>4</d>
<d>4</d>
</Subcolumn>
</YColumn>
<YColumn Width="92" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d/>
<d/>
<d>5</d>
<d>5</d>
<d>5</d>
<d>5</d>
<d>5</d>
<d>5</d>
<d>5</d>
<d>5</d>
<d>5</d>
</Subcolumn>
</YColumn>
<YColumn Width="92" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d/>
<d/>
<d>6</d>
<d>6</d>
<d>6</d>
<d>6</d>
<d>6</d>
<d>6</d>
<d>6</d>
</Subcolumn>
</YColumn>
<YColumn Width="92" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d/>
<d/>
<d>7</d>
<d/>
<d>7</d>
<d>7</d>
<d>7</d>
<d>7</d>
<d>7</d>
</Subcolumn>
</YColumn>
<YColumn Width="92" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d/>
<d/>
<d/>
<d/>
<d>8</d>
<d>8</d>
<d>8</d>
<d>8</d>
<d>8</d>
</Subcolumn>
</YColumn>
<YColumn Width="92" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d/>
<d/>
<d/>
<d/>
<d>9</d>
<d>9</d>
<d>9</d>
<d>9</d>
<d>9</d>
</Subcolumn>
</YColumn>
<YColumn Width="92" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d/>
<d/>
<d/>
<d/>
<d>10</d>
<d>10</d>
<d>10</d>
<d>10</d>
<d>10</d>
</Subcolumn>
</YColumn>
<YColumn Width="92" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d/>
<d/>
<d/>
<d/>
<d>11</d>
<d>11</d>
<d>11</d>
<d>11</d>
<d>11</d>
</Subcolumn>
</YColumn>
<YColumn Width="92" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d/>
<d/>
<d/>
<d/>
<d>12</d>
<d>12</d>
<d>12</d>
<d/>
<d>12</d>
</Subcolumn>
</YColumn>
<YColumn Width="92" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d/>
<d/>
<d/>
<d/>
<d>13</d>
<d>13</d>
<d>13</d>
<d/>
<d>13</d>
</Subcolumn>
</YColumn>
<YColumn Width="92" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d/>
<d/>
<d/>
<d/>
<d/>
<d>14</d>
</Subcolumn>
</YColumn>
<YColumn Width="92" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d/>
<d/>
<d/>
<d/>
<d/>
<d>15</d>
</Subcolumn>
</YColumn>
<YColumn Width="92" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d/>
<d/>
<d/>
<d/>
<d/>
<d>16</d>
</Subcolumn>
</YColumn>
<YColumn Width="92" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d/>
<d/>
<d/>
<d/>
<d/>
<d>17</d>
</Subcolumn>
</YColumn>
</Table>

<!--Analyses, graphs and layouts as compressed binary. Don't edit this part of the file.-->

<Template xmlns:dt="urn:schemas-microsoft-com:datatypes" dt:dt="bin.base64">eNrsXQ98E0W+n92k6Wbzp+kfoCjq8lfEUtLSAlWUQIGjvsJV7R2gnDZtFhpIk9KktOC/qKCg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=</Template></GraphPadPrismFile>
