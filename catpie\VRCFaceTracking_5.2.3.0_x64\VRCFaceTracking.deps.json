{"runtimeTarget": {"name": ".NETCoreApp,Version=v7.0/win10-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v7.0": {}, ".NETCoreApp,Version=v7.0/win10-x64": {"VRCFaceTracking/1.0.0": {"dependencies": {"CommunityToolkit.Labs.WinUI.SettingsControls": "0.0.18", "CommunityToolkit.Mvvm": "8.2.2", "CommunityToolkit.WinUI.UI.Controls": "7.1.2", "Microsoft.Extensions.Hosting": "6.0.1", "Microsoft.WindowsAppSDK": "1.2.230118.102", "Microsoft.Xaml.Behaviors.WinUI.Managed": "2.0.9", "Sentry": "4.3.0", "Sentry.Extensions.Logging": "4.3.0", "VRCFaceTracking.Core": "1.0.0", "WinUIEx": "2.1.0", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "7.0.17", "runtimepack.Microsoft.Windows.SDK.NET.Ref": "10.0.19041.31"}, "runtime": {"VRCFaceTracking.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/7.0.17": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "********", "fileVersion": "12.0.1724.11508"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "7.0.1724.11508"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}, "netstandard.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1724.11508"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.29.30152.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "7.0.1724.11508"}, "clretwrc.dll": {"fileVersion": "7.0.1724.11508"}, "clrgc.dll": {"fileVersion": "7.0.1724.11508"}, "clrjit.dll": {"fileVersion": "7.0.1724.11508"}, "coreclr.dll": {"fileVersion": "7.0.1724.11508"}, "createdump.exe": {"fileVersion": "7.0.1724.11508"}, "hostfxr.dll": {"fileVersion": "7.0.1724.11508"}, "hostpolicy.dll": {"fileVersion": "7.0.1724.11508"}, "mscordaccore.dll": {"fileVersion": "7.0.1724.11508"}, "mscordaccore_amd64_amd64_7.0.1724.11508.dll": {"fileVersion": "7.0.1724.11508"}, "mscordbi.dll": {"fileVersion": "7.0.1724.11508"}, "mscorrc.dll": {"fileVersion": "7.0.1724.11508"}, "msquic.dll": {"fileVersion": "*******"}}}, "runtimepack.Microsoft.Windows.SDK.NET.Ref/10.0.19041.31": {"runtime": {"Microsoft.Windows.SDK.NET.dll": {"assemblyVersion": "10.0.19041.24", "fileVersion": "10.0.19041.31"}, "WinRT.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.4.44211"}}}, "ColorCode.Core/2.0.13": {"dependencies": {"NETStandard.Library": "1.6.1"}, "runtime": {"lib/netstandard1.4/ColorCode.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*********"}}}, "ColorCode.WinUI/2.0.13": {"dependencies": {"ColorCode.Core": "2.0.13", "Microsoft.WindowsAppSDK": "1.2.230118.102"}, "runtime": {"lib/net5.0-windows10.0.18362/ColorCode.WinUI.dll": {"assemblyVersion": "*******", "fileVersion": "*********"}}}, "CommunityToolkit.Common/7.1.2": {"runtime": {"lib/net5.0/CommunityToolkit.Common.dll": {"assemblyVersion": "7.1.0.0", "fileVersion": "7.1.2.1"}}}, "CommunityToolkit.Labs.WinUI.SettingsControls/0.0.18": {"dependencies": {"CommunityToolkit.WinUI.UI": "7.1.2", "Microsoft.WindowsAppSDK": "1.2.230118.102"}, "runtime": {"lib/net60-windows10.0.19041.0/CommunityToolkit.Labs.WinUI.SettingsControls.dll": {"assemblyVersion": "0.0.18.0", "fileVersion": "0.0.18.0"}}}, "CommunityToolkit.Mvvm/8.2.2": {"runtime": {"lib/net6.0/CommunityToolkit.Mvvm.dll": {"assemblyVersion": "8.2.0.0", "fileVersion": "8.2.2.1"}}}, "CommunityToolkit.WinUI/7.1.2": {"dependencies": {"CommunityToolkit.Common": "7.1.2", "Microsoft.WindowsAppSDK": "1.2.230118.102"}, "runtime": {"lib/net5.0-windows10.0.18362/CommunityToolkit.WinUI.dll": {"assemblyVersion": "7.1.0.0", "fileVersion": "7.1.2.1"}}}, "CommunityToolkit.WinUI.UI/7.1.2": {"dependencies": {"CommunityToolkit.WinUI": "7.1.2", "Microsoft.WindowsAppSDK": "1.2.230118.102"}, "runtime": {"lib/net5.0-windows10.0.18362/CommunityToolkit.WinUI.UI.dll": {"assemblyVersion": "7.1.0.0", "fileVersion": "7.1.2.1"}}}, "CommunityToolkit.WinUI.UI.Controls/7.1.2": {"dependencies": {"CommunityToolkit.WinUI.UI.Controls.Core": "7.1.2", "CommunityToolkit.WinUI.UI.Controls.DataGrid": "7.1.2", "CommunityToolkit.WinUI.UI.Controls.Input": "7.1.2", "CommunityToolkit.WinUI.UI.Controls.Layout": "7.1.2", "CommunityToolkit.WinUI.UI.Controls.Markdown": "7.1.2", "CommunityToolkit.WinUI.UI.Controls.Media": "7.1.2", "CommunityToolkit.WinUI.UI.Controls.Primitives": "7.1.2", "Microsoft.WindowsAppSDK": "1.2.230118.102"}}, "CommunityToolkit.WinUI.UI.Controls.Core/7.1.2": {"dependencies": {"CommunityToolkit.WinUI": "7.1.2", "CommunityToolkit.WinUI.UI.Controls.Primitives": "7.1.2", "Microsoft.WindowsAppSDK": "1.2.230118.102"}, "runtime": {"lib/net5.0-windows10.0.18362/CommunityToolkit.WinUI.UI.Controls.Core.dll": {"assemblyVersion": "7.1.0.0", "fileVersion": "7.1.2.1"}}}, "CommunityToolkit.WinUI.UI.Controls.DataGrid/7.1.2": {"dependencies": {"Microsoft.WindowsAppSDK": "1.2.230118.102"}, "runtime": {"lib/net5.0-windows10.0.18362/CommunityToolkit.WinUI.UI.Controls.DataGrid.dll": {"assemblyVersion": "7.1.0.0", "fileVersion": "7.1.2.1"}}}, "CommunityToolkit.WinUI.UI.Controls.Input/7.1.2": {"dependencies": {"CommunityToolkit.WinUI.UI.Controls.Primitives": "7.1.2", "Microsoft.WindowsAppSDK": "1.2.230118.102"}, "runtime": {"lib/net5.0-windows10.0.18362/CommunityToolkit.WinUI.UI.Controls.Input.dll": {"assemblyVersion": "7.1.0.0", "fileVersion": "7.1.2.1"}}}, "CommunityToolkit.WinUI.UI.Controls.Layout/7.1.2": {"dependencies": {"CommunityToolkit.WinUI.UI": "7.1.2", "Microsoft.WindowsAppSDK": "1.2.230118.102"}, "runtime": {"lib/net5.0-windows10.0.18362/CommunityToolkit.WinUI.UI.Controls.Layout.dll": {"assemblyVersion": "7.1.0.0", "fileVersion": "7.1.2.1"}}}, "CommunityToolkit.WinUI.UI.Controls.Markdown/7.1.2": {"dependencies": {"ColorCode.WinUI": "2.0.13", "CommunityToolkit.WinUI.UI": "7.1.2", "CommunityToolkit.WinUI.UI.Controls.Core": "7.1.2", "Microsoft.WindowsAppSDK": "1.2.230118.102"}, "runtime": {"lib/net5.0-windows10.0.18362/CommunityToolkit.WinUI.UI.Controls.Markdown.dll": {"assemblyVersion": "7.1.0.0", "fileVersion": "7.1.2.1"}}}, "CommunityToolkit.WinUI.UI.Controls.Media/7.1.2": {"dependencies": {"CommunityToolkit.WinUI.UI": "7.1.2", "Microsoft.Graphics.Win2D": "1.0.0.30", "Microsoft.WindowsAppSDK": "1.2.230118.102", "System.Text.Json": "6.0.0"}, "runtime": {"lib/net5.0-windows10.0.18362/CommunityToolkit.WinUI.UI.Controls.Media.dll": {"assemblyVersion": "7.1.0.0", "fileVersion": "7.1.2.1"}}}, "CommunityToolkit.WinUI.UI.Controls.Primitives/7.1.2": {"dependencies": {"CommunityToolkit.WinUI.UI": "7.1.2", "Microsoft.WindowsAppSDK": "1.2.230118.102"}, "runtime": {"lib/net5.0-windows10.0.18362/CommunityToolkit.WinUI.UI.Controls.Primitives.dll": {"assemblyVersion": "7.1.0.0", "fileVersion": "7.1.2.1"}}}, "Microsoft.Extensions.Configuration/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Configuration.Binder/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Configuration.CommandLine/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/6.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.222.6406"}}}, "Microsoft.Extensions.Configuration.FileExtensions/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Physical": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Configuration.Json/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "System.Text.Json": "6.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Configuration.UserSecrets/6.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Json": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Physical": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.222.6406"}}}, "Microsoft.Extensions.DependencyInjection/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"runtime": {"lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.FileProviders.Physical/6.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileSystemGlobbing": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.FileSystemGlobbing/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Hosting/6.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.Configuration.CommandLine": "6.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "6.0.1", "Microsoft.Extensions.Configuration.FileExtensions": "6.0.0", "Microsoft.Extensions.Configuration.Json": "6.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "6.0.1", "Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Physical": "6.0.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "6.0.0", "Microsoft.Extensions.Logging.Console": "6.0.0", "Microsoft.Extensions.Logging.Debug": "6.0.0", "Microsoft.Extensions.Logging.EventLog": "6.0.0", "Microsoft.Extensions.Logging.EventSource": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.222.6406"}}}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Http/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Logging/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Configuration/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Logging.Console/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Text.Json": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Logging.Debug/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Logging.EventLog/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.EventLog": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Logging.EventSource/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Json": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Options/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Primitives/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Graphics.Win2D/1.0.0.30": {"runtime": {"lib/net5.0-windows10.0.18362.0/Microsoft.Graphics.Canvas.Interop.dll": {"assemblyVersion": "1.0.26.0", "fileVersion": "1.0.26.0"}}, "native": {"runtimes/win10-x64/native/Microsoft.Graphics.Canvas.dll": {"fileVersion": "1.0.0.0"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.win.Microsoft.Win32.Primitives": "4.3.0"}}, "Microsoft.Windows.SDK.BuildTools/10.0.22621.1": {}, "Microsoft.WindowsAppSDK/1.2.230118.102": {"dependencies": {"Microsoft.Windows.SDK.BuildTools": "10.0.22621.1"}, "runtime": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.22622.1036"}, "lib/net6.0-windows10.0.18362.0/Microsoft.WinUI.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.2301"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Projection.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.PushNotifications.Projection.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Power.Projection.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Projection.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Widgets.Projection.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.0.0"}}, "native": {"runtimes/win10-x64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"fileVersion": "0.0.0.0"}}}, "Microsoft.Xaml.Behaviors.WinUI.Managed/2.0.9": {"dependencies": {"Microsoft.WindowsAppSDK": "1.2.230118.102"}, "runtime": {"lib/net5.0-windows10.0.17763.0/Microsoft.Xaml.Interactions.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}, "lib/net5.0-windows10.0.17763.0/Microsoft.Xaml.Interactivity.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "Newtonsoft.Json/13.0.2": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.2.27524"}}}, "runtime.any.System.Collections/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "runtime.any.System.Diagnostics.Tools/4.3.0": {}, "runtime.any.System.Diagnostics.Tracing/4.3.0": {}, "runtime.any.System.Globalization/4.3.0": {}, "runtime.any.System.Globalization.Calendars/4.3.0": {}, "runtime.any.System.IO/4.3.0": {}, "runtime.any.System.Reflection/4.3.0": {}, "runtime.any.System.Reflection.Extensions/4.3.0": {}, "runtime.any.System.Reflection.Primitives/4.3.0": {}, "runtime.any.System.Resources.ResourceManager/4.3.0": {}, "runtime.any.System.Runtime/4.3.0": {"dependencies": {"System.Private.Uri": "4.3.0"}}, "runtime.any.System.Runtime.Handles/4.3.0": {}, "runtime.any.System.Runtime.InteropServices/4.3.0": {}, "runtime.any.System.Text.Encoding/4.3.0": {}, "runtime.any.System.Text.Encoding.Extensions/4.3.0": {}, "runtime.any.System.Threading.Tasks/4.3.0": {}, "runtime.any.System.Threading.Timer/4.3.0": {}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "runtime.win7-x64.runtime.native.System.IO.Compression": "4.3.0"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.win.Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "runtime.win.System.Console/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "runtime.win.System.Diagnostics.Debug/4.3.0": {}, "runtime.win.System.IO.FileSystem/4.3.0": {"dependencies": {"System.Buffers": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Overlapped": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "runtime.win.System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.Win32.Primitives": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0"}}, "runtime.win.System.Net.Sockets/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Net.NameResolution": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Principal.Windows": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Overlapped": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "runtime.win.System.Runtime.Extensions/4.3.0": {"dependencies": {"System.Private.Uri": "4.3.0"}}, "runtime.win7-x64.runtime.native.System.IO.Compression/4.3.0": {"native": {"runtimes/win7-x64/native/clrcompression.dll": {"fileVersion": "4.6.24705.1"}}}, "runtime.win7.System.Private.Uri/4.3.0": {}, "Sentry/4.3.0": {"runtime": {"lib/net6.0/Sentry.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Sentry.Extensions.Logging/4.3.0": {"dependencies": {"Microsoft.Extensions.Http": "6.0.0", "Microsoft.Extensions.Logging.Configuration": "6.0.0", "Sentry": "4.3.0"}, "runtime": {"lib/net6.0/Sentry.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Buffers/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Collections": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.win.System.Console": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.win.System.Diagnostics.Debug": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.EventLog/6.0.0": {"runtime": {"runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Diagnostics.Tools": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Diagnostics.Tracing": "4.3.0"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Globalization": "4.3.0"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Globalization.Calendars": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.any.System.IO": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Buffers": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.win.System.IO.FileSystem": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Net.NameResolution/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Principal.Windows": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "runtime.win.System.Net.Primitives": "4.3.0"}}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.win.System.Net.Sockets": "4.3.0"}}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Private.Uri/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "runtime.win7.System.Private.Uri": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection": "4.3.0"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection.Extensions": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection.Primitives": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Resources.ResourceManager": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "runtime.any.System.Runtime": "4.3.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.win.System.Runtime.Extensions": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "runtime.any.System.Runtime.InteropServices": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Security.Claims/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Security.Principal": "4.3.0"}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Cng/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Principal/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Security.Principal.Windows/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.Win32.Primitives": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Claims": "4.3.0", "System.Security.Principal": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Text.Encoding": "4.3.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.any.System.Text.Encoding.Extensions": "4.3.0"}}, "System.Text.Encodings.Web/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Json/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.0"}}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Overlapped/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Threading.Timer": "4.3.0"}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.3.0"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "WinUIEx/2.1.0": {"dependencies": {"Microsoft.WindowsAppSDK": "1.2.230118.102"}, "runtime": {"lib/net6.0-windows10.0.18362/WinUIEx.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "VRCFaceTracking.Core/1.0.0": {"dependencies": {"CommunityToolkit.Mvvm": "8.2.2", "Microsoft.Extensions.Hosting": "6.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Newtonsoft.Json": "13.0.2", "Sentry": "4.3.0", "VRCFaceTracking.SDK": "1.0.0"}, "runtime": {"VRCFaceTracking.Core.dll": {}}}, "VRCFaceTracking.SDK/1.0.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"VRCFaceTracking.SDK.dll": {}}}}}, "libraries": {"VRCFaceTracking/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/7.0.17": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.Windows.SDK.NET.Ref/10.0.19041.31": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "ColorCode.Core/2.0.13": {"type": "package", "serviceable": true, "sha512": "sha512-v0xqipj/6h2TpIKSrOCZpXMmOGvLsyO2prWBXY6t+lduexDSimqKIZXTTZkpq/vG2kNxB8K8nzyrkIB3usoN7A==", "path": "colorcode.core/2.0.13", "hashPath": "colorcode.core.2.0.13.nupkg.sha512"}, "ColorCode.WinUI/2.0.13": {"type": "package", "serviceable": true, "sha512": "sha512-FPuyaXOb9LaD6nSUXtMrK/l564tmRbCfMiQWrnKzzLhJnnRuWkU2JpSJjV2KGCrjyx5ol7wy7TiK0GnGnPsIEA==", "path": "colorcode.winui/2.0.13", "hashPath": "colorcode.winui.2.0.13.nupkg.sha512"}, "CommunityToolkit.Common/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-KnHnukN+Sc26YT98OWYTp8mvaYlLc61lzDh0RrVRNQXm01hZ+iXAjCgKM59vAojqxNVrlbcXj22LYOyXWDItgg==", "path": "communitytoolkit.common/7.1.2", "hashPath": "communitytoolkit.common.7.1.2.nupkg.sha512"}, "CommunityToolkit.Labs.WinUI.SettingsControls/0.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-VKGUWwNpkDAz4dX+Telk7fT834hqBCGZ9/JVsECvbS+3Iwva3LZJMEsqsZfVyU5b9o2w7y+8qkvPPN0ZYcyUpQ==", "path": "communitytoolkit.labs.winui.settingscontrols/0.0.18", "hashPath": "communitytoolkit.labs.winui.settingscontrols.0.0.18.nupkg.sha512"}, "CommunityToolkit.Mvvm/8.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-r0g0k9tGYdrnz8R7T3x5UiokDffeevzK/2P/9SBL6fqLgN8B157MIi/bVUWI1KAz6ZorZrK9AdABCWUeXZZsvA==", "path": "communitytoolkit.mvvm/8.2.2", "hashPath": "communitytoolkit.mvvm.8.2.2.nupkg.sha512"}, "CommunityToolkit.WinUI/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-0O6hCHy+/adJygcROD3r3yA41gRq972i/r6uGUfo8lfEbr/I1YGePTClAh1P9JYT7DdoI2IqeUhX05lq+rwrHg==", "path": "communitytoolkit.winui/7.1.2", "hashPath": "communitytoolkit.winui.7.1.2.nupkg.sha512"}, "CommunityToolkit.WinUI.UI/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-jF1GdEJsZX5o81ZfLjSjgC5RuYwQneuW2mdFIb7FDFzITsgqCn7mfn7BglpNiXvjExkocmAKbBgmCcbp0Nypmw==", "path": "communitytoolkit.winui.ui/7.1.2", "hashPath": "communitytoolkit.winui.ui.7.1.2.nupkg.sha512"}, "CommunityToolkit.WinUI.UI.Controls/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-+aDc1KqmlYM+HE0Mw5OnfQBOFhY0c7zp1aGz/4qs+1I+Xk8Ot3IRwvs8dYxBEGCD9YzE2OzaJLPztBpQ9JEWig==", "path": "communitytoolkit.winui.ui.controls/7.1.2", "hashPath": "communitytoolkit.winui.ui.controls.7.1.2.nupkg.sha512"}, "CommunityToolkit.WinUI.UI.Controls.Core/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-7MVYQKjYlD5SryWNy5FA7Pcer9Rphjk7hCrSIOT9m4dcsOYmekZf36TOJARKxySXvlmjNud6QkmWssJKFC4k5w==", "path": "communitytoolkit.winui.ui.controls.core/7.1.2", "hashPath": "communitytoolkit.winui.ui.controls.core.7.1.2.nupkg.sha512"}, "CommunityToolkit.WinUI.UI.Controls.DataGrid/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-F73oYdYza8zhDwGHdXCZJJMeiDJ6nR97WPe180o7jOX3U90ZIqvU/GtYZdN3/x8DOS48t5GQG70LqxfyIGeaxw==", "path": "communitytoolkit.winui.ui.controls.datagrid/7.1.2", "hashPath": "communitytoolkit.winui.ui.controls.datagrid.7.1.2.nupkg.sha512"}, "CommunityToolkit.WinUI.UI.Controls.Input/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-fT8s21NI51YCR/mgm1eabwnSSQuXnQRz/TqHKjzYABhfGSvl44YlvuAQzbE9VfZSEUtiv4kesgylrzKd4/dArg==", "path": "communitytoolkit.winui.ui.controls.input/7.1.2", "hashPath": "communitytoolkit.winui.ui.controls.input.7.1.2.nupkg.sha512"}, "CommunityToolkit.WinUI.UI.Controls.Layout/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-FpKo2DdJgREAvttTYhOJTy1ru6ODRG41Da+vMrB7bcqavdbPgSTqYzolLcgvMl2InpIdt+fqn6U6mGQYoV+ksQ==", "path": "communitytoolkit.winui.ui.controls.layout/7.1.2", "hashPath": "communitytoolkit.winui.ui.controls.layout.7.1.2.nupkg.sha512"}, "CommunityToolkit.WinUI.UI.Controls.Markdown/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-0hAhwBfWa7aWKYOPGxhJJXowjrncY4he8jizcbuPVCgv8tZS1LXEEH4MoGKq57iLKz2lG3qaVN+1trcDWYq0Sg==", "path": "communitytoolkit.winui.ui.controls.markdown/7.1.2", "hashPath": "communitytoolkit.winui.ui.controls.markdown.7.1.2.nupkg.sha512"}, "CommunityToolkit.WinUI.UI.Controls.Media/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-FFH1mNF+rp3GEIQOceoxzSrRVY3G8aA+sYoK6Fv1kDEFWlCfLYF+cSCexL4sBuw0ARs1QXUvNqW4T1F/nAWsdw==", "path": "communitytoolkit.winui.ui.controls.media/7.1.2", "hashPath": "communitytoolkit.winui.ui.controls.media.7.1.2.nupkg.sha512"}, "CommunityToolkit.WinUI.UI.Controls.Primitives/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-mNtVRpBVnFzSywRzzLPXBIoYm4UWJPZkpNPNAu75lnl4KcnkfjjyQg90hkfF9MT/JkBJQG0z5XOdMCksn7EiMQ==", "path": "communitytoolkit.winui.ui.controls.primitives/7.1.2", "hashPath": "communitytoolkit.winui.ui.controls.primitives.7.1.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tq2wXyh3fL17EMF2bXgRhU7JrbO3on93MRKYxzz4JzzvuGSA1l0W3GI9/tl8EO89TH+KWEymP7bcFway6z9fXg==", "path": "microsoft.extensions.configuration/6.0.0", "hashPath": "microsoft.extensions.configuration.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qWzV9o+ZRWq+pGm+1dF+R7qTgTYoXvbyowRoBxQJGfqTpqDun2eteerjRQhq5PQ/14S+lqto3Ft4gYaRyl4rdQ==", "path": "microsoft.extensions.configuration.abstractions/6.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3<PERSON>rKzND8LIC7o08QAVlKfaEIYEvLJbtmVbFZVBRXeu9YkKfSSzLZfR1SUfQPBIy9mKLhEtJgGYImkcMNaKE0A==", "path": "microsoft.extensions.configuration.binder/6.0.0", "hashPath": "microsoft.extensions.configuration.binder.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3nL1qCkZ1Oxx14ZTzgo4MmlO7tso7F+TtMZAY2jUAtTLyAcDp+EDjk3RqafoKiNaePyPvvlleEcBxh3b2Hzl1g==", "path": "microsoft.extensions.configuration.commandline/6.0.0", "hashPath": "microsoft.extensions.configuration.commandline.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-pnyXV1LFOsYjGveuC07xp0YHIyGq7jRq5Ncb5zrrIieMLWVwgMyYxcOH0jTnBedDT4Gh1QinSqsjqzcieHk1og==", "path": "microsoft.extensions.configuration.environmentvariables/6.0.1", "hashPath": "microsoft.extensions.configuration.environmentvariables.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V4Dth2cYMZpw3HhGw9XUDIijpI6gN+22LDt0AhufIgOppCUfpWX4483OmN+dFXRJkJLc8Tv0Q8QK+1ingT2+KQ==", "path": "microsoft.extensions.configuration.fileextensions/6.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GJGery6QytCzS/BxJ96klgG9in3uH26KcUBbiVG/coNDXCRq6LGVVlUT4vXq34KPuM+R2av+LeYdX9h4IZOCUg==", "path": "microsoft.extensions.configuration.json/6.0.0", "hashPath": "microsoft.extensions.configuration.json.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Fy8yr4V6obi7ZxvKYI1i85jqtwMq8tqyxQVZpRSkgeA8enqy/KvBIMdcuNdznlxQMZa72mvbHqb7vbg4Pyx95w==", "path": "microsoft.extensions.configuration.usersecrets/6.0.1", "hashPath": "microsoft.extensions.configuration.usersecrets.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-k6PWQMuoBDGGHOQTtyois2u4AwyVcIwL2LaSLlTZQm2CYcJ1pxbt6jfAnpWmzENA/wfrYRI/X9DTLoUkE4AsLw==", "path": "microsoft.extensions.dependencyinjection/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0pd4/fho0gC12rQswaGQxbU34jOS1TPS8lZPpkFCH68ppQjHNHYle9iRuHeev1LhrJ94YPvzcRd8UmIuFk23Qw==", "path": "microsoft.extensions.fileproviders.abstractions/6.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QvkL7l0nM8udt3gfyu0Vw8bbCXblxaKOl7c2oBfgGy4LCURRaL9XWZX1FWJrQc43oMokVneVxH38iz+bY1sbhg==", "path": "microsoft.extensions.fileproviders.physical/6.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ip8jnL1aPiaPeKINCqaTEbvBFDmVx9dXQEBZ2HOBRXPD1eabGNqP/bKlsIcp7U2lGxiXd5xIhoFcmY8nM4Hdiw==", "path": "microsoft.extensions.filesystemglobbing/6.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hbmizc9KPWOacLU8Z8YMaBG6KWdZFppczYV/KwnPGU/8xebWxQxdDeJmLOgg968prb7g2oQgnp6JVLX6lgby8g==", "path": "microsoft.extensions.hosting/6.0.1", "hashPath": "microsoft.extensions.hosting.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GcT5l2CYXL6Sa27KCSh0TixsRfADUgth+ojQSD5EkzisZxmGFh7CwzkcYuGwvmXLjr27uWRNrJ2vuuEjMhU05Q==", "path": "microsoft.extensions.hosting.abstractions/6.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-15+pa2G0bAMHbHewaQIdr/y6ag2H3yh4rd9hTXavtWDzQBkvpe2RMqFg8BxDpcQWssmjmBApGPcw93QRz6YcMg==", "path": "microsoft.extensions.http/6.0.0", "hashPath": "microsoft.extensions.http.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eIbyj40QDg1NDz0HBW0S5f3wrLVnKWnDJ/JtZ+yJDFnDj90VoPuoPmFkeaXrtu+0cKm5GRAwoDf+dBWXK0TUdg==", "path": "microsoft.extensions.logging/6.0.0", "hashPath": "microsoft.extensions.logging.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZDskjagmBAbv+K8rYW9VhjPplhbOE63xUD0DiuydZJwt15dRyoqicYklLd86zzeintUc7AptDkHn+YhhYkYo8A==", "path": "microsoft.extensions.logging.configuration/6.0.0", "hashPath": "microsoft.extensions.logging.configuration.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-gsqKzOEdsvq28QiXFxagmn1oRB9GeI5GgYCkoybZtQA0IUb7QPwf1WmN3AwJeNIsadTvIFQCiVK0OVIgKfOBGg==", "path": "microsoft.extensions.logging.console/6.0.0", "hashPath": "microsoft.extensions.logging.console.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-M9g/JixseSZATJE9tcMn9uzoD4+DbSglivFqVx8YkRJ7VVPmnvCEbOZ0AAaxsL1EKyI4cz07DXOOJExxNsUOHw==", "path": "microsoft.extensions.logging.debug/6.0.0", "hashPath": "microsoft.extensions.logging.debug.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rlo0RxlMd0WtLG3CHI0qOTp6fFn7MvQjlrCjucA31RqmiMFCZkF8CHNbe8O7tbBIyyoLGWB1he9CbaA5iyHthg==", "path": "microsoft.extensions.logging.eventlog/6.0.0", "hashPath": "microsoft.extensions.logging.eventlog.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-BeDyyqt7nkm/nr+Gdk+L8n1tUT/u33VkbXAOesgYSNsxDM9hJ1NOBGoZfj9rCbeD2+9myElI6JOVVFmnzgeWQA==", "path": "microsoft.extensions.logging.eventsource/6.0.0", "hashPath": "microsoft.extensions.logging.eventsource.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dzXN0+V1AyjOe2xcJ86Qbo233KHuLEY0njf/P2Kw8SfJU+d45HNS2ctJdnEnrWbM9Ye2eFgaC5Mj9otRMU6IsQ==", "path": "microsoft.extensions.options/6.0.0", "hashPath": "microsoft.extensions.options.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXWINbTn0vC0FYc9GaQTISbxhQLAMrvtbuvD9N6JelEaIS/Pr62wUCinrq5bf1WRBGczt1v4wDhxFtVFNcMdUQ==", "path": "microsoft.extensions.options.configurationextensions/6.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9+PnzmQFfEFNR9J2aDTfJGGupShHjOuGw4VUv+JB044biSHrnmCIMD+mJHmb2H7YryrfBEXDurxQ47gJZdCKNQ==", "path": "microsoft.extensions.primitives/6.0.0", "hashPath": "microsoft.extensions.primitives.6.0.0.nupkg.sha512"}, "Microsoft.Graphics.Win2D/1.0.0.30": {"type": "package", "serviceable": true, "sha512": "sha512-pEGf7FSx2dRWJuoaSoXk+WYkZfYwhWoJKOyyo0XLRU6V2Ul02MjxRhoxggrzGz6jVFiT7R2NdPdV8M6N1F+QfA==", "path": "microsoft.graphics.win2d/1.0.0.30", "hashPath": "microsoft.graphics.win2d.1.0.0.30.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Windows.SDK.BuildTools/10.0.22621.1": {"type": "package", "serviceable": true, "sha512": "sha512-Sp1DkYvg7yxuhamwxv+qFC66KC3paKQpwK8Q1J6XuAh6nzXIInmsDcpJ3szr0XGud4ysXojqwTfGdW01gvZ/0g==", "path": "microsoft.windows.sdk.buildtools/10.0.22621.1", "hashPath": "microsoft.windows.sdk.buildtools.10.0.22621.1.nupkg.sha512"}, "Microsoft.WindowsAppSDK/1.2.230118.102": {"type": "package", "serviceable": true, "sha512": "sha512-MyHPzz4NSo+ulXkQ1sVo83SaZTFUcz429oy1m7lTfaxyU5St48p3TgzKKFRwe3WtpUf9OAUReYBCczIkojfmTQ==", "path": "microsoft.windowsappsdk/1.2.230118.102", "hashPath": "microsoft.windowsappsdk.1.2.230118.102.nupkg.sha512"}, "Microsoft.Xaml.Behaviors.WinUI.Managed/2.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-rGIUCwQZB1degP2+Cc7hBbGOwuokyUxMh97umT+dpqJ8X71y2DKsXrKzDDQc8lKdWbloJbVDpzB9X42yZiS8tA==", "path": "microsoft.xaml.behaviors.winui.managed/2.0.9", "hashPath": "microsoft.xaml.behaviors.winui.managed.2.0.9.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-R2pZ3B0UjeyHShm9vG+Tu0EBb2lC8b0dFzV9gVn50ofHXh9Smjk6kTn7A/FdAsC8B5cKib1OnGYOXxRBz5XQDg==", "path": "newtonsoft.json/13.0.2", "hashPath": "newtonsoft.json.13.0.2.nupkg.sha512"}, "runtime.any.System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-23g6rqftKmovn2cLeGsuHUYm0FD7pdutb0uQMJpZ3qTvq+zHkgmt6J65VtRry4WDGYlmkMa4xDACtaQ94alNag==", "path": "runtime.any.system.collections/4.3.0", "hashPath": "runtime.any.system.collections.4.3.0.nupkg.sha512"}, "runtime.any.System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-S/GPBmfPBB48ZghLxdDR7kDAJVAqgAuThyDJho3OLP5OS4tWD2ydyL8LKm8lhiBxce10OKe9X2zZ6DUjAqEbPg==", "path": "runtime.any.system.diagnostics.tools/4.3.0", "hashPath": "runtime.any.system.diagnostics.tools.4.3.0.nupkg.sha512"}, "runtime.any.System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1lpifymjGDzoYIaam6/Hyqf8GhBI3xXYLK2TgEvTtuZMorG3Kb9QnMTIKhLjJYXIiu1JvxjngHvtVFQQlpQ3HQ==", "path": "runtime.any.system.diagnostics.tracing/4.3.0", "hashPath": "runtime.any.system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "runtime.any.System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sMDBnad4rp4t7GY442Jux0MCUuKL4otn5BK6Ni0ARTXTSpRNBzZ7hpMfKSvnVSED5kYJm96YOWsqV0JH0d2uuw==", "path": "runtime.any.system.globalization/4.3.0", "hashPath": "runtime.any.system.globalization.4.3.0.nupkg.sha512"}, "runtime.any.System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-M1r+760j1CNA6M/ZaW6KX8gOS8nxPRqloqDcJYVidRG566Ykwcs29AweZs2JF+nMOCgWDiMfPSTMfvwOI9F77w==", "path": "runtime.any.system.globalization.calendars/4.3.0", "hashPath": "runtime.any.system.globalization.calendars.4.3.0.nupkg.sha512"}, "runtime.any.System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SDZ5AD1DtyRoxYtEcqQ3HDlcrorMYXZeCt7ZhG9US9I5Vva+gpIWDGMkcwa5XiKL0ceQKRZIX2x0XEjLX7PDzQ==", "path": "runtime.any.system.io/4.3.0", "hashPath": "runtime.any.system.io.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-hLC3A3rI8jipR5d9k7+f0MgRCW6texsAp0MWkN/ci18FMtQ9KH7E2vDn/DH2LkxsszlpJpOn9qy6Z6/69rH6eQ==", "path": "runtime.any.system.reflection/4.3.0", "hashPath": "runtime.any.system.reflection.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cPhT+Vqu52+cQQrDai/V91gubXUnDKNRvlBnH+hOgtGyHdC17aQIU64EaehwAQymd7kJA5rSrVRNfDYrbhnzyA==", "path": "runtime.any.system.reflection.extensions/4.3.0", "hashPath": "runtime.any.system.reflection.extensions.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nrm1p3armp6TTf2xuvaa+jGTTmncALWFq22CpmwRvhDf6dE9ZmH40EbOswD4GnFLrMRS0Ki6Kx5aUPmKK/hZBg==", "path": "runtime.any.system.reflection.primitives/4.3.0", "hashPath": "runtime.any.system.reflection.primitives.4.3.0.nupkg.sha512"}, "runtime.any.System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Lxb89SMvf8w9p9+keBLyL6H6x/TEmc6QVsIIA0T36IuyOY3kNvIdyGddA2qt35cRamzxF8K5p0Opq4G4HjNbhQ==", "path": "runtime.any.system.resources.resourcemanager/4.3.0", "hashPath": "runtime.any.system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "runtime.any.System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fRS7zJgaG9NkifaAxGGclDDoRn9HC7hXACl52Or06a/fxdzDajWb5wov3c6a+gVSlekRoexfjwQSK9sh5um5LQ==", "path": "runtime.any.system.runtime/4.3.0", "hashPath": "runtime.any.system.runtime.4.3.0.nupkg.sha512"}, "runtime.any.System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GG84X6vufoEzqx8PbeBKheE4srOhimv+yLtGb/JkR3Y2FmoqmueLNFU4Xx8Y67plFpltQSdK74x0qlEhIpv/CQ==", "path": "runtime.any.system.runtime.handles/4.3.0", "hashPath": "runtime.any.system.runtime.handles.4.3.0.nupkg.sha512"}, "runtime.any.System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lBoFeQfxe/4eqjPi46E0LU/YaCMdNkQ8B4MZu/mkzdIAZh8RQ1NYZSj0egrQKdgdvlPFtP4STtob40r4o2DBAw==", "path": "runtime.any.system.runtime.interopservices/4.3.0", "hashPath": "runtime.any.system.runtime.interopservices.4.3.0.nupkg.sha512"}, "runtime.any.System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+ihI5VaXFCMVPJNstG4O4eo1CfbrByLxRrQQTqOTp1ttK0kUKDqOdBSTaCB2IBk/QtjDrs6+x4xuezyMXdm0HQ==", "path": "runtime.any.system.text.encoding/4.3.0", "hashPath": "runtime.any.system.text.encoding.4.3.0.nupkg.sha512"}, "runtime.any.System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NLrxmLsfRrOuVqPWG+2lrQZnE53MLVeo+w9c54EV+TUo4c8rILpsDXfY8pPiOy9kHpUHHP07ugKmtsU3vVW5Jg==", "path": "runtime.any.system.text.encoding.extensions/4.3.0", "hashPath": "runtime.any.system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "runtime.any.System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OhBAVBQG5kFj1S+hCEQ3TUHBAEtZ3fbEMgZMRNdN8A0Pj4x+5nTELEqL59DU0TjKVE6II3dqKw4Dklb3szT65w==", "path": "runtime.any.system.threading.tasks/4.3.0", "hashPath": "runtime.any.system.threading.tasks.4.3.0.nupkg.sha512"}, "runtime.any.System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-w4ehZJ+AwXYmGwYu+rMvym6RvMaRiUEQR1u6dwcyuKHxz8Heu/mO9AG1MquEgTyucnhv3M43X0iKpDOoN17C0w==", "path": "runtime.any.system.threading.timer/4.3.0", "hashPath": "runtime.any.system.threading.timer.4.3.0.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HdSSp5MnJSsg08KMfZThpuLPJpPwE5hBXvHwoKWosyHHfe8Mh5WKT0ylEOf6yNzX6Ngjxe4Whkafh5q7Ymac4Q==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+yH1a49wJMy8Zt4yx5RhJrxO/DBDByAiCzNwiETI+1S4mPdCu0OY4djdciC7Vssk0l22wQaDLrXxXkp+3+7bVA==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c3YNH1GQJbfIPJeCnr4avseugSqPrxwIqzthYyZDN6EuOyNOzq+y2KSUfRcXauya1sF4foESTgwM5e1A8arAKw==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NS1U+700m4KFRHR5o4vo9DSlTmlCKu/u7dtE5sUHVIPB+xpXxYQvgBgA6wEIeCz6Yfn0Z52/72WYsToCEPJnrw==", "path": "runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3pthNgxxFcD+Pc0WSEoC0+md3MyhRS6aCEeenvNE3Fdw1HyJ18ZhRFVJJzIeR/O/jpxPboB805Ho0T3Ul7w8A==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KeLz4HClKf+nFS7p/6Fi/CqyLXh81FpiGzcmuS8DGi9lUqSnZ6Es23/gv2O+1XVGfrbNmviF7CckBpavkBoIFQ==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X7IdhILzr4ROXd8mI1BUCQMSHSQwelUlBjF1JyTKCjXaOGn2fB4EKBxQbCK2VjO3WaWIdlXZL3W6TiIVnrhX4g==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nyFNiCk/r+VOiIqreLix8yN+q3Wga9+SE8BCgkf+2BwEKiNx6DyvFjCgkfV743/grxv8jHJ8gUK4XEQw7yzRYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ytoewC6wGorL7KoCAvRfsgoJPJbNq+64k2SqW6JcOAebWsFUvCCYgfzQMrnpvPiEl4OrblUlhF2ji+Q1+SVLrQ==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I8bKw2I8k58Wx7fMKQJn2R8lamboCAiHfHeV/pS65ScKWMMI0+wJkLYlEKvgW1D/XvSl/221clBoR2q9QNNM7A==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VB5cn/7OzUfzdnC8tqAIMQciVLiq2epm2NrAm1E9OjNRyG4lVhfR61SMcLizejzQP8R8Uf/0l5qOIbUEi+RdEg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.win.Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NU51SEt/ZaD2MF48sJ17BIqx7rjeNNLXUevfMOjqQIetdndXwYjZfZsT6jD+rSWp/FYxjesdK4xUSl4OTEI0jw==", "path": "runtime.win.microsoft.win32.primitives/4.3.0", "hashPath": "runtime.win.microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "runtime.win.System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RRACWygml5dnmfgC1SW6tLGsFgwsUAKFtvhdyHnIEz4EhWyrd7pacDdY95CacQJy7BMXRDRCejC9aCRC0Y1sQA==", "path": "runtime.win.system.console/4.3.0", "hashPath": "runtime.win.system.console.4.3.0.nupkg.sha512"}, "runtime.win.System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-hHHP0WCStene2jjeYcuDkETozUYF/3sHVRHAEOgS3L15hlip24ssqCTnJC28Z03Wpo078oMcJd0H4egD2aJI8g==", "path": "runtime.win.system.diagnostics.debug/4.3.0", "hashPath": "runtime.win.system.diagnostics.debug.4.3.0.nupkg.sha512"}, "runtime.win.System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z37zcSCpXuGCYtFbqYO0TwOVXxS2d+BXgSoDFZmRg8BC4Cuy54edjyIvhhcfCrDQA9nl+EPFTgHN54dRAK7mNA==", "path": "runtime.win.system.io.filesystem/4.3.0", "hashPath": "runtime.win.system.io.filesystem.4.3.0.nupkg.sha512"}, "runtime.win.System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lkXXykakvXUU+Zq2j0pC6EO20lEhijjqMc01XXpp1CJN+DeCwl3nsj4t5Xbpz3kA7yQyTqw6d9SyIzsyLsV3zA==", "path": "runtime.win.system.net.primitives/4.3.0", "hashPath": "runtime.win.system.net.primitives.4.3.0.nupkg.sha512"}, "runtime.win.System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FK/2gX6MmuLIKNCGsV59Fe4IYrLrI5n9pQ1jh477wiivEM/NCXDT2dRetH5FSfY0bQ+VgTLcS3zcmjQ8my3nxQ==", "path": "runtime.win.system.net.sockets/4.3.0", "hashPath": "runtime.win.system.net.sockets.4.3.0.nupkg.sha512"}, "runtime.win.System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RkgHVhUPvzZxuUubiZe8yr/6CypRVXj0VBzaR8hsqQ8f+rUo7e4PWrHTLOCjd8fBMGWCrY//fi7Ku3qXD7oHRw==", "path": "runtime.win.system.runtime.extensions/4.3.0", "hashPath": "runtime.win.system.runtime.extensions.4.3.0.nupkg.sha512"}, "runtime.win7-x64.runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UamDlgSO/nIzc96M+g3wbvAGbAuXjvRYR5Ttm/FVJgt2iva8ouOqSJ0j6eGI7pZDLvD/ZISl9XRZOajE/Xvizg==", "path": "runtime.win7-x64.runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.win7-x64.runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.win7.System.Private.Uri/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Q+IBgaPYicSQs2tBlmXqbS25c/JLIthWrgrpMwxKSOobW/OqIMVFruUGfuaz4QABVzV8iKdCAbN7APY7Tclbnw==", "path": "runtime.win7.system.private.uri/4.3.0", "hashPath": "runtime.win7.system.private.uri.4.3.0.nupkg.sha512"}, "Sentry/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-H91fl+qg58QmsRhLCoHOLP6aCqE0vLytf4VavRLRhCGjqExCxJkyA4TC4YhX+3SPV3s0cCMHPu2f3vtRcAaYVw==", "path": "sentry/4.3.0", "hashPath": "sentry.4.3.0.nupkg.sha512"}, "Sentry.Extensions.Logging/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NbL9FnFHnQQDP4cjfVVddbIXBXI65JN0iDZ2hMlV4yc9YdA4VxXVP2y5WovP9CffrEbEeYL/4UCUT5ZxQNp+Ow==", "path": "sentry.extensions.logging/4.3.0", "hashPath": "sentry.extensions.logging.4.3.0.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ratu44uTIHgeBeI0dE8DWvmXVBSo4u7ozRZZHOMmK/JPpYyo0dAfgSiHlpiObMQ5lEtEyIXA40sKRYg5J6A8uQ==", "path": "system.buffers/4.3.0", "hashPath": "system.buffers.4.3.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-frQDfv0rl209cKm1lnwTgFPzNigy2EKk1BS3uAvHvlBVKe5cymGyHO+Sj+NLv5VF/AhHsqPIUUwya5oV4CHMUw==", "path": "system.diagnostics.diagnosticsource/6.0.0", "hashPath": "system.diagnostics.diagnosticsource.6.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lcyUiXTsETK2ALsZrX+nWuHSIQeazhqPphLfaRxzdGaG93+0kELqpgEHtwWOlQe7+jSFnKwaCAgL4kjeZCQJnw==", "path": "system.diagnostics.eventlog/6.0.0", "hashPath": "system.diagnostics.eventlog.6.0.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "path": "system.io.compression.zipfile/4.3.0", "hashPath": "system.io.compression.zipfile.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sYg+FtILtRQuYWSIAuNOELwVuVsxVyJGWQyOnlAzhV4xvhyFnON1bAzYYC+jjRW8JREM45R0R5Dgi8MTC5sEwA==", "path": "system.net.http/4.3.0", "hashPath": "system.net.http.4.3.0.nupkg.sha512"}, "System.Net.NameResolution/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-AFYl08R7MrsrEjqpQWTZWBadqXyTzNDaWpMqyxhb0d6sGhV6xMDKueuBXlLL30gz+DIRY6MpdgnHWlCh5wmq9w==", "path": "system.net.nameresolution/4.3.0", "hashPath": "system.net.nameresolution.4.3.0.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "path": "system.net.sockets/4.3.0", "hashPath": "system.net.sockets.4.3.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Private.Uri/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I4SwANiUGho1esj4V4oSlPllXjzCZDE+5XXso2P03LW2vOda2Enzh8DWOxwN6hnrJyp314c7KuVu31QYhRzOGg==", "path": "system.private.uri/4.3.0", "hashPath": "system.private.uri.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Security.Claims/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-P/+BR/2lnc4PNDHt/TPBAWHVMLMRHsyYZbU1NphW4HIWzCggz8mJbTQQ3MKljFE7LS3WagmVFuBgoLcFzYXlkA==", "path": "system.security.claims/4.3.0", "hashPath": "system.security.claims.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-03idZOqFlsKRL4W+LuCpJ6dBYDUWReug6lZjBa3uJWnk5sPCUXckocevTaUA8iT/MFSrY/2HXkOt753xQ/cf8g==", "path": "system.security.cryptography.cng/4.3.0", "hashPath": "system.security.cryptography.cng.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Principal/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I1tkfQlAoMM2URscUtpcRo/hX0jinXx6a/KUtEQoz3owaYwl3qwsO8cbzYVVnjxrzxjHo3nJC+62uolgeGIS9A==", "path": "system.security.principal/4.3.0", "hashPath": "system.security.principal.4.3.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HVL1rvqYtnRCxFsYag/2le/ZfKLK4yMw79+s6FmKXbSCNN0JeAhrYxnRAHFoWRa0dEojsDcbBSpH3l22QxAVyw==", "path": "system.security.principal.windows/4.3.0", "hashPath": "system.security.principal.windows.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "path": "system.text.encodings.web/6.0.0", "hashPath": "system.text.encodings.web.6.0.0.nupkg.sha512"}, "System.Text.Json/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zaJsHfESQvJ11vbXnNlkrR46IaMULk/gHxYsJphzSF+07kTjPHv+Oc14w6QEOfo3Q4hqLJgStUaYB9DBl0TmWg==", "path": "system.text.json/6.0.0", "hashPath": "system.text.json.6.0.0.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Overlapped/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m3HQ2dPiX/DSTpf+yJt8B0c+SRvzfqAJKx+QDWi+VLhz8svLT23MVjEOHPF/KiSLeArKU/iHescrbLd3yVgyNg==", "path": "system.threading.overlapped/4.3.0", "hashPath": "system.threading.overlapped.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-npvJkVKl5rKXrtl1Kkm6OhOUaYGEiF9wFbppFRWSMoApKzt2PiPHT2Bb8a5sAWxprvdOAtvaARS9QYMznEUtug==", "path": "system.threading.tasks.extensions/4.3.0", "hashPath": "system.threading.tasks.extensions.4.3.0.nupkg.sha512"}, "System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "path": "system.threading.timer/4.3.0", "hashPath": "system.threading.timer.4.3.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "WinUIEx/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-4tiyOThWn2J4ps6mvo8tWQlx/oFLM02Sm7Jj7Awt/ODFzJT7fOYMxDfGfMJmqd8F7LyiIy5JnjmOMZdXQFYtHA==", "path": "winuiex/2.1.0", "hashPath": "winuiex.2.1.0.nupkg.sha512"}, "VRCFaceTracking.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "VRCFaceTracking.SDK/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}, "runtimes": {"win10-x64": ["win10", "win81-x64", "win81", "win8-x64", "win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win10-x64-aot": ["win10-aot", "win10-x64", "win10", "win81-x64-aot", "win81-aot", "win81-x64", "win81", "win8-x64-aot", "win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"]}}