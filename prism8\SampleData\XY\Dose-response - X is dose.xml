<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="#"?>
<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:dt="urn:schemas-microsoft-com:datatypes" xmlns:ps="http://graphpad.com/prism/Prism.htm" version="1.0">
	<!--
	XML style sheet template for formatting data and info tables from GraphPad Prism 7.0.
	Copyright 1992-2016 GraphPad Software, Inc.
-->
	<xsl:output method="html" version="4.0" omit-xml-declaration="yes"/>
	<GraphPadPrismFile xmlns="http://graphpad.com/prism/Prism.htm" PrismXMLVersion="5.00">
<Created>
<OriginalVersion CreatedByProgram="GraphPad Prism" CreatedByVersion="7.0.0.90" Login="Harvey" DateTime="2015-09-08T10:42:05-08:00"/>
<MostRecentVersion CreatedByProgram="GraphPad Prism" CreatedByVersion="7.0.0.140" Login="Alex" DateTime="2016-02-24T11:28:55+02:00"/>
</Created>
<DefGraphButton ButtonID="101"/>
<InfoSequence>
</InfoSequence>
<TableSequence Selected="1">

<Ref ID="Table0" Selected="1"/>
</TableSequence>
<Table ID="Table0" XFormat="numbers" YFormat="replicates" Replicates="3" TableType="XY" EVFormat="AsteriskAfterNumber">
<Title>dose vs. response</Title>
<FloatingNote ID="Sticky4" Auto="0" Color="Yellow" Left="219" Top="272" Width="654" Height="433" ScrWidth="1920" ScrHeight="1080" ScrDPI="96">
<WebLink Flags="0" ToolTip="@help:REG_Dose_vs._response" URL="@help:REG_DR_stim_variable_2">
How to fit and plot dose-response curves.
</WebLink>

<B><Font Size="11" Color="#000000" Face="Verdana">
How the data are organized
</Font></B>
<Font Size="11" Color="#000000" Face="Verdana">
<BR/>The X values are molar concentration.  
</Font>
<Font Size="11" Color="#000000" Face="Verdana">
<BR/><BR/>The Y values are responses (in triplicate) in two experimental conditions. Note that treatment groups are not defined by a separate grouping variable column, but rather by separate Y columns. Blank cells represent missing data.
</Font>
<B><Font Size="11" Color="#000000" Face="Verdana">
<BR/><BR/>The goal
</Font></B>
<Font Size="11" Color="#000000" Face="Verdana">
<BR/>To fit a dose-response curve to determine the EC50 of the drug, and its Hill Slope, in both conditions.
</Font>
<B><Font Size="11" Color="#000000" Face="Verdana">
<BR/><BR/>How to fit a dose vs. response curve
</Font></B>
<Font Size="11" Color="#000000" Face="Verdana">
<BR/>1. Click Analyze, choose nonlinear regression, and choose the dose-response (stimulation) set of equations, and choose [Dose] vs. response -- variable slope. You can accept all the defaults in the rest of the dialog (but make sure the Parameters tab is set to report the asymmetrical confidence intervals).
</Font>
<Font Size="11" Color="#000000" Face="Verdana">
<BR/><BR/>2. On the linked graph, double-click on the  X axis and (in the upper right of the dialog) choose a logarithmic axis.  Also consider adding nine minor ticks with logarithmic spacing, and to use the powers-of-ten numbering format. Instead of plotting mean and SD, consider plotting the raw data (plot each replicate).
</Font>
</FloatingNote>

<XColumn Width="95" Subcolumns="1" Decimals="6">
<Title>[Agonist], M</Title>
<Subcolumn>
<d>0</d>
<d>1e-008</d>
<d>3e-008</d>
<d>1e-007</d>
<d>3e-007</d>
<d>0.000001</d>
<d>0.000003</d>
<d>0.00001</d>
<d>0.00003</d>
<d>0.0001</d>
<d>0.0003</d>
</Subcolumn>
</XColumn>
<XAdvancedColumn Version="1" Width="95" Decimals="6" Subcolumns="1">
<Title>[Agonist], M</Title>
<Subcolumn>
<d>0</d>
<d>1e-008</d>
<d>3e-008</d>
<d>1e-007</d>
<d>3e-007</d>
<d>0.000001</d>
<d>0.000003</d>
<d>0.00001</d>
<d>0.00003</d>
<d>0.0001</d>
<d>0.0003</d>
</Subcolumn>
</XAdvancedColumn>
<YColumn Width="276" Decimals="0" Subcolumns="3">
<Title>No inhibitor</Title>
<Subcolumn>
<d>0</d>
<d>11</d>
<d>125</d>
<d>190</d>
<d>258</d>
<d>322</d>
<d>354</d>
<d>348</d>
<d/>
<d>412</d>
</Subcolumn>
<Subcolumn>
<d>3</d>
<d>33</d>
<d>141</d>
<d>218</d>
<d>289</d>
<d>353</d>
<d>359</d>
<d>298</d>
<d/>
<d>378</d>
</Subcolumn>
<Subcolumn>
<d>2</d>
<d>25</d>
<d>160</d>
<d>196</d>
<d>345</d>
<d>328</d>
<d>369</d>
<d>372</d>
<d/>
<d>399</d>
</Subcolumn>
</YColumn>
<YColumn Width="276" Decimals="0" Subcolumns="3">
<Title>Inhibitor </Title>
<Subcolumn>
<d>3</d>
<d/>
<d>11</d>
<d>52</d>
<d>80</d>
<d>171</d>
<d>289</d>
<d>272</d>
<d>359</d>
<d>352</d>
<d>389</d>
</Subcolumn>
<Subcolumn>
<d>5</d>
<d/>
<d>25</d>
<d>55</d>
<d>77</d>
<d>195</d>
<d>230</d>
<d>333</d>
<d>306</d>
<d>320</d>
<d>338</d>
</Subcolumn>
<Subcolumn>
<d>4</d>
<d/>
<d>28</d>
<d>61</d>
<d>44</d>
<d>246</d>
<d>243</d>
<d>310</d>
<d>297</d>
<d>365</d>
<d/>
</Subcolumn>
</YColumn>
</Table>
</GraphPadPrismFile>
	<xsl:template match="ps:Info">
		<a><xsl:attribute name="name"><xsl:value-of select="@ID"/></xsl:attribute></a>
		<table border="0">
			<tr><td><xsl:apply-templates select="ps:Title"/> </td><td width="20"></td><td><b>Notes</b></td></tr>
			<tr><td valign="top">
					<table BORDER="1" CELLSPACING="0">
						<tr><th>Constant</th><th>Name</th></tr>
						<xsl:for-each select="ps:Constant">
							<tr>
								<td><xsl:apply-templates select="ps:Name"/> </td>
								<td><xsl:apply-templates select="ps:Value"/> </td>
							</tr>
						</xsl:for-each>
					</table>
				</td>
				<td width="20">
				</td>
				<td valign="top">
					<xsl:apply-templates select="ps:Notes"/>
				</td></tr>
		</table>
	</xsl:template>
	<xsl:template match="ps:GraphPadPrismFile">
		<HTML>
			<BODY>
				<xsl:variable name="Chars" select="'ABCDEFGHIJKLMNOPQRSTUVWXYZ'"/>
				<font color="#999999">
					<p align="center">This file can be opened by <a href="http://www.graphpad.com">GraphPad</a> Prism (version 
	<xsl:value-of select="@PrismXMLVersion"/> or later). </p></font><hr/>
         This file contains <xsl:value-of select="count(ps:Table|ps:HugeTable)"/> data tables and 
	<xsl:value-of select="count(ps:Info)+count(ps:Table|ps:HugeTable/ps:Info)"/> info tables:

	<dir>
					<xsl:for-each select="ps:Info">
						<li>
							<a>
								<xsl:attribute name="href">#<xsl:value-of select="@ID"/></xsl:attribute>
								<xsl:apply-templates select="ps:Title"/>
							</a>
						</li>
					</xsl:for-each>
					<xsl:for-each select="ps:Table|ps:HugeTable">
						<li>
							<a>
								<xsl:attribute name="href">#<xsl:value-of select="@ID"/></xsl:attribute>
								<xsl:apply-templates select="ps:Title"/>
							</a>
							<dir>
								<xsl:for-each select="ps:Info">
									<li>
										<a>
											<xsl:attribute name="href">#<xsl:value-of select="@ID"/></xsl:attribute>
											<xsl:apply-templates select="ps:Title"/>
										</a>
									</li>
								</xsl:for-each>
							</dir>
						</li>
					</xsl:for-each>
				</dir>
				<hr/>
				<xsl:apply-templates select="ps:Info"/>
				<br/>
				<hr/>
				<br/>
				<xsl:for-each select="ps:Table|ps:HugeTable">
					<xsl:apply-templates select="ps:Title"/>
					<a><xsl:attribute name="name"><xsl:value-of select="@ID"/></xsl:attribute></a>
					<xsl:variable name="width" select="100 div count (*/ps:Subcolumn)"/>
					<xsl:variable name="xFormat" select="./@XFormat"/>
					<xsl:variable name="yFormat" select="./@YFormat"/>
					<xsl:variable name="nRepl" select="./@replicates"/>
					<xsl:variable name="isXAdvancedColumn" select="boolean(($xFormat='date' or $xFormat='time' or $xFormat='startenddate') and count(ps:XAdvancedColumn) &gt; 0)"/>
					<xsl:variable name="isXAdvancedSubcol" select="boolean(($xFormat='startenddate') or count(ps:XAdvancedColumn/ps:Subcolumn/ps:Title) &gt; 0)"/>
					<xsl:variable name="isXSubcol" select="boolean(($xFormat='error' or count(ps:XColumn/ps:Subcolumn/ps:Title) &gt; 0) and not ($isXAdvancedColumn))"/>
					<xsl:variable name="isYSubcol" select="boolean(($yFormat='replicates'and ./@Replicates&gt;1) or 
						 ($yFormat!='text'and $yFormat!='replicates') or 
						count(ps:YColumn/ps:Subcolumn/ps:Title)&gt;0)"/>
					<xsl:variable name="isYSubColNameCustom" select="boolean($isYSubcol and count(ps:SubColumnTitles)&gt;0)"/>
					<TABLE BORDER="1" CELLSPACING="0">
						<TR>
							<xsl:for-each select="ps:RowTitlesColumn">
								<TD align="center">
									<xsl:attribute name="rowspan">
										<xsl:if test="$isXSubcol or $isXAdvancedSubcol or $isYSubcol">2</xsl:if>
									</xsl:attribute><BR/>
								</TD>
							</xsl:for-each>
							<xsl:if test="not ($isXAdvancedColumn)">
								<xsl:for-each select="ps:XColumn">
									<TD align="center">
										<xsl:attribute name="rowspan">
											<xsl:if test="$isYSubcol and not($isXSubcol)">2</xsl:if>
										</xsl:attribute>
										<xsl:attribute name="colspan"><xsl:value-of select="./@Subcolumns"/></xsl:attribute>
						 
						<xsl:apply-templates select="ps:Title"/>
										<xsl:if test="count(ps:Title)=0 or ps:Title=''">X-Title</xsl:if>				
						 
					</TD>
								</xsl:for-each>
							</xsl:if>
							<xsl:if test="$isXAdvancedColumn">
								<xsl:for-each select="ps:XAdvancedColumn">
									<TD align="center">
										<xsl:attribute name="rowspan">
											<xsl:if test="$isYSubcol and not($isXAdvancedSubcol)">2</xsl:if>
										</xsl:attribute>
										<xsl:attribute name="colspan"><xsl:value-of select="./@Subcolumns"/></xsl:attribute>
						 
						<xsl:apply-templates select="ps:Title"/>
										<xsl:if test="count(ps:Title)=0 or ps:Title=''">X-Title</xsl:if>				
						 
					</TD>
								</xsl:for-each>
							</xsl:if>
							<xsl:for-each select="ps:YColumn">
								<xsl:variable name="DefColName2" select="(position()-1) mod 26"/>
								<xsl:variable name="DefColName1" select="(position()-1-$DefColName2) div 26"/>
								<TD align="center">
									<xsl:attribute name="colspan"><xsl:value-of select="./@Subcolumns"/></xsl:attribute>
									<xsl:attribute name="rowspan">
										<xsl:if test="($isXSubcol or $isXAdvancedSubcol) and not($isYSubcol)">2</xsl:if>
									</xsl:attribute>
					 
					<xsl:apply-templates select="ps:Title"/>
									<xsl:if test="count(ps:Title)=0 or ps:Title=''">
										<xsl:value-of select="'Data Set-'"/>
										<xsl:if test="$DefColName1 &gt; 0">
											<xsl:value-of select="substring($Chars,$DefColName1,1)"/>
										</xsl:if>
										<xsl:value-of select="substring($Chars,$DefColName2+1,1)"/>
									</xsl:if>
					 				
	   			</TD>
							</xsl:for-each>
						</TR>
						<xsl:if test="$isXSubcol or $isXAdvancedSubcol or $isYSubcol">
							<TR>
								<xsl:if test="$isXSubcol">
									<xsl:for-each select="ps:XColumn">
										<xsl:for-each select="ps:Subcolumn">
											<TD align="center"> 
					 
					<xsl:apply-templates select="ps:Title"/>
												<xsl:if test="count(ps:Title)=0 or ps:Title=''">
													<xsl:choose>
														<xsl:when test="position()=1"><B>X</B></xsl:when>
														<xsl:when test="position()=2"><B>Err.Bar</B></xsl:when>
													</xsl:choose>
												</xsl:if>				
					 
				</TD>
										</xsl:for-each>
									</xsl:for-each>
								</xsl:if>
								<xsl:if test="$isXAdvancedSubcol">
									<xsl:for-each select="ps:XAdvancedColumn">
										<xsl:for-each select="ps:Subcolumn">
											<TD align="center"> 
					 
					<xsl:apply-templates select="ps:Title"/>
												<xsl:if test="count(ps:Title)=0 or ps:Title=''">
													<xsl:choose>
														<xsl:when test="position()=1"><B>Starting Date</B></xsl:when>
														<xsl:when test="position()=2"><B>Ending Date</B></xsl:when>
													</xsl:choose>
												</xsl:if>				
					 
				</TD>
										</xsl:for-each>
									</xsl:for-each>
								</xsl:if>
								<xsl:if test="$isYSubcol">
									<xsl:for-each select="ps:YColumn">
										<xsl:variable name="DefColName2" select="(position()-1) mod 26"/>
										<xsl:variable name="DefColName1" select="(position()-1-$DefColName2) div 26"/>
										<xsl:variable name="YColumnNdx" select="position()"/>
										<xsl:for-each select="ps:Subcolumn">
											<TD align="center">
					 
					<xsl:apply-templates select="ps:Title"/>
												<xsl:if test="count(ps:Title)=0 or ps:Title=''">
													<xsl:if test="$yFormat='replicates' or $yFormat='text'">
														<B>
															<xsl:if test="$DefColName1 &gt; 0">
																<xsl:value-of select="substring($Chars,$DefColName1,1)"/>
															</xsl:if>
															<xsl:choose>
																<xsl:when test="$isYSubColNameCustom">
																	<xsl:variable name="subColCustomNamePos" select="position()"/>
																	<xsl:for-each select="../../ps:SubColumnTitles/ps:Subcolumn">
																		<xsl:if test="position()=$subColCustomNamePos">
																			<xsl:choose>
																				<xsl:when test="../../ps:SubColumnTitles/@OwnSet='1'">
																					<xsl:variable name="DItemsCount" select="count(./ps:d)"/>
																					<xsl:choose>
																						<xsl:when test="$DItemsCount&lt;$YColumnNdx or ./ps:d[$YColumnNdx]=''">
																							<xsl:value-of select="substring($Chars,$DefColName2+1,1)"/>:Y<xsl:value-of select="position()"/>
																						</xsl:when>
																						<xsl:otherwise>
																							<xsl:apply-templates select="./ps:d[$YColumnNdx]"/>
																						</xsl:otherwise>
																					</xsl:choose>
																				</xsl:when>
																				<xsl:when test="../../ps:SubColumnTitles/@OwnSet='0'">
																					<xsl:variable name="DItemsCount" select="count(./ps:d)"/>
																					<xsl:choose>
																						<xsl:when test="$DItemsCount&lt;1 or ./ps:d[1]=''">
																							<xsl:value-of select="substring($Chars,$DefColName2+1,1)"/>:Y<xsl:value-of select="position()"/>
																						</xsl:when>
																						<xsl:otherwise>
																							<xsl:apply-templates select="./ps:d[1]"/>
																						</xsl:otherwise>
																					</xsl:choose>
																				</xsl:when>
																				<xsl:otherwise>
																					<xsl:apply-templates select="ps:d"/>
																					<xsl:if test="ps:d=''">
																						<xsl:value-of select="substring($Chars,$DefColName2+1,1)"/>:Y<xsl:value-of select="position()"/>
																					</xsl:if>
																				</xsl:otherwise>
																			</xsl:choose>
																		</xsl:if>
																	</xsl:for-each>
																</xsl:when>
																<xsl:otherwise>
																	<xsl:value-of select="substring($Chars,$DefColName2+1,1)"/>:Y<xsl:value-of select="position()"/>
																</xsl:otherwise>
															</xsl:choose>
														</B>
													</xsl:if>
													<xsl:if test="$yFormat!='replicates' and $yFormat!='text'">
														<xsl:if test="not($yFormat='replicates' or $yFormat='text')">
															<xsl:if test="position()=1"><B>Mean</B></xsl:if>
															<xsl:if test="position()!=1">
																<xsl:choose>
																	<xsl:when test="$yFormat='SD'">
																		<B>SD</B>
																	</xsl:when>
																	<xsl:when test="$yFormat='SE'">
																		<B>SEM</B>
																	</xsl:when>
																	<xsl:when test="$yFormat='CV'">
																		<B>%CV</B>
																	</xsl:when>
																	<xsl:when test="$yFormat='SDN'">
																		<xsl:if test="position()=2"><B>SD</B></xsl:if>
																		<xsl:if test="position()=3"><B>N</B></xsl:if>
																	</xsl:when>
																	<xsl:when test="$yFormat='SEN'">
																		<xsl:if test="position()=2"><B>SEM</B></xsl:if>
																		<xsl:if test="position()=3"><B>N</B></xsl:if>
																	</xsl:when>
																	<xsl:when test="$yFormat='CVN'">
																		<xsl:if test="position()=2"><B>%CV</B></xsl:if>
																		<xsl:if test="position()=3"><B>N</B></xsl:if>
																	</xsl:when>
																	<xsl:when test="$yFormat='low-high'">
																		<xsl:if test="position()=2"><B>+Error</B></xsl:if>
																		<xsl:if test="position()=3"><B>-Error</B></xsl:if>
																	</xsl:when>
																	<xsl:when test="$yFormat='upper-lower-limits'">
																		<xsl:if test="position()=2"><B>UpperLimit</B></xsl:if>
																		<xsl:if test="position()=3"><B>LowerLimit</B></xsl:if>
																	</xsl:when>
																</xsl:choose>
															</xsl:if>
														</xsl:if>
													</xsl:if>
												</xsl:if>
					 				
	   			</TD>
										</xsl:for-each>
									</xsl:for-each>
								</xsl:if>
							</TR>
						</xsl:if>
						<TR>
							<xsl:for-each select="ps:RowTitlesColumn/ps:Subcolumn">
								<td valign="top" nowrap="1">
									<xsl:attribute name="width"><xsl:value-of select="$width"/>%</xsl:attribute>
									<xsl:apply-templates select="ps:d"/>
									<xsl:if test="count(ps:d)=0"><br/></xsl:if>
								</td>
							</xsl:for-each>
							<xsl:if test="not($isXAdvancedColumn)">
								<xsl:for-each select="ps:XColumn/ps:Subcolumn">
									<td valign="top" nowrap="1">
										<xsl:attribute name="width"><xsl:value-of select="$width"/>%</xsl:attribute>
										<xsl:apply-templates select="ps:d"/>
										<xsl:if test="count(ps:d)=0"><br/></xsl:if>
									</td>
								</xsl:for-each>
							</xsl:if>
							<xsl:if test="$isXAdvancedColumn">
								<xsl:for-each select="ps:XAdvancedColumn/ps:Subcolumn">
									<td valign="top" nowrap="1">
										<xsl:attribute name="width"><xsl:value-of select="$width"/>%</xsl:attribute>
										<xsl:apply-templates select="ps:d"/>
										<xsl:if test="count(ps:d)=0"><br/></xsl:if>
									</td>
								</xsl:for-each>
							</xsl:if>
							<xsl:for-each select="ps:YColumn/ps:Subcolumn">
								<td valign="top" nowrap="1">
									<xsl:attribute name="width"><xsl:value-of select="$width"/>%</xsl:attribute>
									<xsl:apply-templates select="ps:d"/>
									<xsl:if test="count(ps:d)=0"><br/></xsl:if>
								</td>
							</xsl:for-each>
						</TR>
					</TABLE>
					<br/>
					<xsl:apply-templates select="ps:Info"/>
					<br/>
					<hr/>
					<br/>
				</xsl:for-each>
			</BODY>
		</HTML>
	</xsl:template>
	<!--<xsl:template match="ps:d">
	<xsl:choose>
		<xsl:when test="@Excluded and string-length(text())" >
		    <font color="#0000ee">
	      		<xsl:apply-templates select="node()"/>*		
		    </font>
		</xsl:when>
	    <xsl:otherwise>
			<xsl:apply-templates select="node()"/>
		</xsl:otherwise>
	</xsl:choose>
	<xsl:if test="position()!=last()"><br/></xsl:if>
</xsl:template>-->
	<xsl:template match="ps:d">
		<xsl:choose>
			<xsl:when test="@Excluded and string-length(text())">
				<xsl:choose>
					<xsl:when test="../../../@EVFormat='AsteriskAfterNumber'">
						<font color="#0000ee">
							<xsl:apply-templates select="node()"/>*
						</font>
					</xsl:when>
					<xsl:when test="../../../@EVFormat='Blank'">
					</xsl:when>
					<xsl:otherwise>
						<xsl:apply-templates select="node()"/>
					</xsl:otherwise>
				</xsl:choose>
			</xsl:when>
			<xsl:otherwise>
				<xsl:apply-templates select="node()"/>
			</xsl:otherwise>
		</xsl:choose>
		<xsl:if test="position()!=last()"><br/></xsl:if>
	</xsl:template>
	<xsl:template match="ps:Title">
		<b><xsl:apply-templates select="node()"/></b>
	</xsl:template>
	<xsl:template match="node()">
		<xsl:copy>
			<xsl:apply-templates select="node()"/>
		</xsl:copy>
	</xsl:template>
	<xsl:template match="ps:Font">
		<font>
			<xsl:if test="@Size">
				<xsl:attribute name="STYLE">font-size:<xsl:value-of select="@Size"/>pt</xsl:attribute>
			</xsl:if>
			<xsl:copy-of select="@*"/>
			<xsl:apply-templates select="node()"/>
		</font>
	</xsl:template>
	<xsl:template match="/">
		<HTML>
			<BODY>
				<xsl:apply-templates select="//ps:GraphPadPrismFile"/>
			</BODY>
		</HTML>
	</xsl:template>
</xsl:stylesheet>
