<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="#"?>
<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:dt="urn:schemas-microsoft-com:datatypes" xmlns:ps="http://graphpad.com/prism/Prism.htm" version="1.0">
	<!--
	XML style sheet template for formatting data and info tables from GraphPad Prism 8.0.
	Copyright 1992-2016 GraphPad Software, Inc.
-->
	<xsl:output method="html" version="4.0" omit-xml-declaration="yes"/>
	<GraphPadPrismFile xmlns="http://graphpad.com/prism/Prism.htm" PrismXMLVersion="5.00">
<Created>
<OriginalVersion CreatedByProgram="GraphPad Prism" CreatedByVersion="8.0.0.142" Login="Harvey" DateTime="2018-02-08T06:45:13-08:00"/>
<MostRecentVersion CreatedByProgram="GraphPad Prism" CreatedByVersion="8.0.0.147" Login="Alex" DateTime="2018-02-14T13:31:49+02:00"/>
</Created>
<InfoSequence>
</InfoSequence>
<TableSequence Selected="1">

<Ref ID="Table0" Selected="1"/>
</TableSequence>
<Table ID="Table0" XFormat="numbers" YFormat="replicates" Replicates="1" TableType="XY" EVFormat="AsteriskAfterNumber">
<Title>Data 1</Title>
<FloatingNote ID="Sticky1" Auto="0" Color="Blue" Left="967" Top="111" Width="274" Height="262" ScrWidth="1920" ScrHeight="1080" ScrDPI="96">

<B><Font Size="10" Color="#000000" Face="Arial">Data source</Font></B>
<Font Size="10" Color="#000000" Face="Arial"><BR/><BR/>These data are used in Chapters 32 and 33 of Intuitive Biostatistics, 4th edition.</Font>
<Font Size="10" Color="#000000" Face="Arial"><BR/><BR/>The data come from Borkman, M., Storlien, L. H., Pan, D. A., Jenkins, A. B., Chisholm, D. J., &amp; Campbell, L. V. (1993). The relation between insulin sensitivity and the fatty-acid composition of skeletal-muscle phospholipids. New England Journal of Medicine, 328, 238-244.</Font>
</FloatingNote>

<FloatingNote ID="Sticky2" Auto="0" Color="Yellow" Left="368" Top="376" Width="293" Height="444" ScrWidth="1920" ScrHeight="1080" ScrDPI="96">
<WebLink Flags="0" ToolTip="@help:reg_finding_the_best-fit_slope_and" URL="@help:reg_finding_the_best-fit_slope_and">
Learn about linear regression
</WebLink>

<B><Font Size="10" Color="#000000" Face="Arial">How the data are organized</Font></B>
<Font Size="10" Color="#000000" Face="Arial"><BR/>The X values are the percentage of a certain kind of fatty acid in muscle (from a biopsy) and the Y values are insulin sensitivity. Each row is a different </Font>
<B><Font Size="10" Color="#000000" Face="Arial"><BR/><BR/>The goal</Font></B>
<Font Size="10" Color="#000000" Face="Arial"><BR/>To use linear regression to fit a straight line through the data </Font>
<B><Font Size="10" Color="#000000" Face="Arial"><BR/><BR/>How to perform linear regression</Font></B>
<Font Size="10" Color="#000000" Face="Arial"><BR/>Click Analyze, and choose Linear regression from the list of XY analyses. </Font>
<Font Size="10" Color="#000000" Face="Arial"><BR/><BR/>Then go to the graph and see that the linear regression line is automatically superimposed. </Font>
<B><Font Size="10" Color="#000000" Face="Arial"><BR/><BR/>Note</Font></B>
<Font Size="10" Color="#000000" Face="Arial"><BR/>In many cases, X is a variable you manipulate and Y is a variable you measure. Here both variables are measured, and it is not completely clear which should be X and which should be Y.</Font>
</FloatingNote>

<XColumn Width="188" Subcolumns="1" Decimals="1">
<Title><TextAlign align="Center">%C20-22 POLYUNSATURATED
 FATTY ACIDS</TextAlign></Title>
<Subcolumn>
<d>17.9</d>
<d>18.3</d>
<d>18.3</d>
<d>18.4</d>
<d>18.4</d>
<d>20.2</d>
<d>20.3</d>
<d>21.8</d>
<d>21.9</d>
<d>22.1</d>
<d>23.1</d>
<d>24.2</d>
<d>24.4</d>
</Subcolumn>
</XColumn>
<XAdvancedColumn Version="1" Width="188" Decimals="1" Subcolumns="1">
<Title><TextAlign align="Center">%C20-22 POLYUNSATURATED
 FATTY ACIDS</TextAlign></Title>
<Subcolumn>
<d>17.9</d>
<d>18.3</d>
<d>18.3</d>
<d>18.4</d>
<d>18.4</d>
<d>20.2</d>
<d>20.3</d>
<d>21.8</d>
<d>21.9</d>
<d>22.1</d>
<d>23.1</d>
<d>24.2</d>
<d>24.4</d>
</Subcolumn>
</XAdvancedColumn>
<YColumn Width="352" Decimals="0" Subcolumns="1">
<Title><TextAlign align="Center">INSULIN SENSITIVITY 
(MG/M<SUP>2</SUP>/MIN)</TextAlign></Title>
<Subcolumn>
<d>250</d>
<d>220</d>
<d>145</d>
<d>115</d>
<d>230</d>
<d>200</d>
<d>330</d>
<d>400</d>
<d>370</d>
<d>260</d>
<d>270</d>
<d>530</d>
<d>375</d>
</Subcolumn>
</YColumn>
</Table>
</GraphPadPrismFile>
	<xsl:template match="ps:Info">
		<a><xsl:attribute name="name"><xsl:value-of select="@ID"/></xsl:attribute></a>
		<table border="0">
			<tr>
				<td><xsl:apply-templates select="ps:Title"/> </td><td width="20"></td><td width="50%"><b>Notes</b></td>
			</tr>
			<tr>
				<td valign="top">
					<table BORDER="1" CELLSPACING="0">
						<tr><th>Constant</th><th>Name</th></tr>
						<xsl:for-each select="ps:Constant">
							<tr>
								<td><xsl:apply-templates select="ps:Name"/> </td>
								<td><xsl:apply-templates select="ps:Value"/> </td>
							</tr>
						</xsl:for-each>
					</table>
				</td>
				<td></td>
				<td valign="top">
					<xsl:apply-templates select="ps:Notes"/>
				</td>
			</tr>
		</table>
	</xsl:template>
	<xsl:template match="ps:GraphPadPrismFile">
		<HTML>
			<BODY>
				<xsl:variable name="Chars" select="'ABCDEFGHIJKLMNOPQRSTUVWXYZ'"/>
				<font color="#999999">
					<p align="center">This file can be opened by <a href="http://www.graphpad.com">GraphPad</a> Prism (version 
	<xsl:value-of select="@PrismXMLVersion"/> or later). </p></font><hr/>
         This file contains <xsl:value-of select="count(ps:Table|ps:HugeTable)"/> data tables and 
	<xsl:value-of select="count(ps:Info)+count(ps:Table|ps:HugeTable/ps:Info)"/> info tables:

	<dir>
					<xsl:for-each select="ps:Info">
						<li>
							<a>
								<xsl:attribute name="href">#<xsl:value-of select="@ID"/></xsl:attribute>
								<xsl:apply-templates select="ps:Title"/>
							</a>
						</li>
					</xsl:for-each>
					<xsl:for-each select="ps:Table|ps:HugeTable">
						<li>
							<a>
								<xsl:attribute name="href">#<xsl:value-of select="@ID"/></xsl:attribute>
								<xsl:apply-templates select="ps:Title"/>
							</a>
							<dir>
								<xsl:for-each select="ps:Info">
									<li>
										<a>
											<xsl:attribute name="href">#<xsl:value-of select="@ID"/></xsl:attribute>
											<xsl:apply-templates select="ps:Title"/>
										</a>
									</li>
								</xsl:for-each>
							</dir>
						</li>
					</xsl:for-each>
				</dir>
				<hr/>
				<xsl:apply-templates select="ps:Info"/>
				<br/>
				<hr/>
				<br/>
				<xsl:for-each select="ps:Table|ps:HugeTable">
					<xsl:apply-templates select="ps:Title"/>
					<a><xsl:attribute name="name"><xsl:value-of select="@ID"/></xsl:attribute></a>
					<xsl:variable name="width" select="100 div count (*/ps:Subcolumn)"/>
					<xsl:variable name="xFormat" select="./@XFormat"/>
					<xsl:variable name="yFormat" select="./@YFormat"/>
					<xsl:variable name="nRepl" select="./@replicates"/>
					<xsl:variable name="isXAdvancedColumn" select="boolean(($xFormat='date' or $xFormat='time' or $xFormat='startenddate') and count(ps:XAdvancedColumn) &gt; 0)"/>
					<xsl:variable name="isXAdvancedSubcol" select="boolean(($xFormat='startenddate') or count(ps:XAdvancedColumn/ps:Subcolumn/ps:Title) &gt; 0)"/>
					<xsl:variable name="isXSubcol" select="boolean(($xFormat='error' or count(ps:XColumn/ps:Subcolumn/ps:Title) &gt; 0) and not ($isXAdvancedColumn))"/>
					<xsl:variable name="isYSubcol" select="boolean(($yFormat='replicates'and ./@Replicates&gt;1) or 
						 ($yFormat!='text'and $yFormat!='replicates') or 
						count(ps:YColumn/ps:Subcolumn/ps:Title)&gt;0)"/>
					<xsl:variable name="isYSubColNameCustom" select="boolean($isYSubcol and count(ps:SubColumnTitles)&gt;0)"/>
					<TABLE BORDER="1" CELLSPACING="0">
						<TR>
							<xsl:for-each select="ps:RowTitlesColumn">
								<TD align="center">
									<xsl:attribute name="rowspan">
										<xsl:if test="$isXSubcol or $isXAdvancedSubcol or $isYSubcol">2</xsl:if>
									</xsl:attribute><BR/>
								</TD>
							</xsl:for-each>
							<xsl:if test="not ($isXAdvancedColumn)">
								<xsl:for-each select="ps:XColumn">
									<TD align="center">
										<xsl:attribute name="rowspan">
											<xsl:if test="$isYSubcol and not($isXSubcol)">2</xsl:if>
										</xsl:attribute>
										<xsl:attribute name="colspan"><xsl:value-of select="./@Subcolumns"/></xsl:attribute>
						 
						<xsl:apply-templates select="ps:Title"/>
										<xsl:if test="count(ps:Title)=0 or ps:Title=''">X-Title</xsl:if>				
						 
					</TD>
								</xsl:for-each>
							</xsl:if>
							<xsl:if test="$isXAdvancedColumn">
								<xsl:for-each select="ps:XAdvancedColumn">
									<TD align="center">
										<xsl:attribute name="rowspan">
											<xsl:if test="$isYSubcol and not($isXAdvancedSubcol)">2</xsl:if>
										</xsl:attribute>
										<xsl:attribute name="colspan"><xsl:value-of select="./@Subcolumns"/></xsl:attribute>
						 
						<xsl:apply-templates select="ps:Title"/>
										<xsl:if test="count(ps:Title)=0 or ps:Title=''">X-Title</xsl:if>				
						 
					</TD>
								</xsl:for-each>
							</xsl:if>
							<xsl:for-each select="ps:YColumn">
								<xsl:variable name="DefColName2" select="(position()-1) mod 26"/>
								<xsl:variable name="DefColName1" select="(position()-1-$DefColName2) div 26"/>
								<TD align="center">
									<xsl:attribute name="colspan"><xsl:value-of select="./@Subcolumns"/></xsl:attribute>
									<xsl:attribute name="rowspan">
										<xsl:if test="($isXSubcol or $isXAdvancedSubcol) and not($isYSubcol)">2</xsl:if>
									</xsl:attribute>
					 
					<xsl:apply-templates select="ps:Title"/>
									<xsl:if test="count(ps:Title)=0 or ps:Title=''">
										<xsl:value-of select="'Data Set-'"/>
										<xsl:if test="$DefColName1 &gt; 0">
											<xsl:value-of select="substring($Chars,$DefColName1,1)"/>
										</xsl:if>
										<xsl:value-of select="substring($Chars,$DefColName2+1,1)"/>
									</xsl:if>
					 				
	   			</TD>
							</xsl:for-each>
						</TR>
						<xsl:if test="$isXSubcol or $isXAdvancedSubcol or $isYSubcol">
							<TR>
								<xsl:if test="$isXSubcol">
									<xsl:for-each select="ps:XColumn">
										<xsl:for-each select="ps:Subcolumn">
											<TD align="center"> 
					 
					<xsl:apply-templates select="ps:Title"/>
												<xsl:if test="count(ps:Title)=0 or ps:Title=''">
													<xsl:choose>
														<xsl:when test="position()=1"><B>X</B></xsl:when>
														<xsl:when test="position()=2"><B>Err.Bar</B></xsl:when>
													</xsl:choose>
												</xsl:if>				
					 
				</TD>
										</xsl:for-each>
									</xsl:for-each>
								</xsl:if>
								<xsl:if test="$isXAdvancedSubcol">
									<xsl:for-each select="ps:XAdvancedColumn">
										<xsl:for-each select="ps:Subcolumn">
											<TD align="center"> 
					 
					<xsl:apply-templates select="ps:Title"/>
												<xsl:if test="count(ps:Title)=0 or ps:Title=''">
													<xsl:choose>
														<xsl:when test="position()=1"><B>Starting Date</B></xsl:when>
														<xsl:when test="position()=2"><B>Ending Date</B></xsl:when>
													</xsl:choose>
												</xsl:if>				
					 
				</TD>
										</xsl:for-each>
									</xsl:for-each>
								</xsl:if>
								<xsl:if test="$isYSubcol">
									<xsl:for-each select="ps:YColumn">
										<xsl:variable name="DefColName2" select="(position()-1) mod 26"/>
										<xsl:variable name="DefColName1" select="(position()-1-$DefColName2) div 26"/>
										<xsl:variable name="YColumnNdx" select="position()"/>
										<xsl:for-each select="ps:Subcolumn">
											<TD align="center">
					 
					<xsl:apply-templates select="ps:Title"/>
												<xsl:if test="count(ps:Title)=0 or ps:Title=''">
													<xsl:if test="$yFormat='replicates' or $yFormat='text'">
														<B>
															<xsl:if test="$DefColName1 &gt; 0">
																<xsl:value-of select="substring($Chars,$DefColName1,1)"/>
															</xsl:if>
															<xsl:choose>
																<xsl:when test="$isYSubColNameCustom">
																	<xsl:variable name="subColCustomNamePos" select="position()"/>
																	<xsl:for-each select="../../ps:SubColumnTitles/ps:Subcolumn">
																		<xsl:if test="position()=$subColCustomNamePos">
																			<xsl:choose>
																				<xsl:when test="../../ps:SubColumnTitles/@OwnSet='1'">
																					<xsl:variable name="DItemsCount" select="count(./ps:d)"/>
																					<xsl:choose>
																						<xsl:when test="$DItemsCount&lt;$YColumnNdx or ./ps:d[$YColumnNdx]=''">
																							<xsl:value-of select="substring($Chars,$DefColName2+1,1)"/>:Y<xsl:value-of select="position()"/>
																						</xsl:when>
																						<xsl:otherwise>
																							<xsl:apply-templates select="./ps:d[$YColumnNdx]"/>
																						</xsl:otherwise>
																					</xsl:choose>
																				</xsl:when>
																				<xsl:when test="../../ps:SubColumnTitles/@OwnSet='0'">
																					<xsl:variable name="DItemsCount" select="count(./ps:d)"/>
																					<xsl:choose>
																						<xsl:when test="$DItemsCount&lt;1 or ./ps:d[1]=''">
																							<xsl:value-of select="substring($Chars,$DefColName2+1,1)"/>:Y<xsl:value-of select="position()"/>
																						</xsl:when>
																						<xsl:otherwise>
																							<xsl:apply-templates select="./ps:d[1]"/>
																						</xsl:otherwise>
																					</xsl:choose>
																				</xsl:when>
																				<xsl:otherwise>
																					<xsl:apply-templates select="ps:d"/>
																					<xsl:if test="ps:d=''">
																						<xsl:value-of select="substring($Chars,$DefColName2+1,1)"/>:Y<xsl:value-of select="position()"/>
																					</xsl:if>
																				</xsl:otherwise>
																			</xsl:choose>
																		</xsl:if>
																	</xsl:for-each>
																</xsl:when>
																<xsl:otherwise>
																	<xsl:value-of select="substring($Chars,$DefColName2+1,1)"/>:Y<xsl:value-of select="position()"/>
																</xsl:otherwise>
															</xsl:choose>
														</B>
													</xsl:if>
													<xsl:if test="$yFormat!='replicates' and $yFormat!='text'">
														<xsl:if test="not($yFormat='replicates' or $yFormat='text')">
															<xsl:if test="position()=1"><B>Mean</B></xsl:if>
															<xsl:if test="position()!=1">
																<xsl:choose>
																	<xsl:when test="$yFormat='SD'">
																		<B>SD</B>
																	</xsl:when>
																	<xsl:when test="$yFormat='SE'">
																		<B>SEM</B>
																	</xsl:when>
																	<xsl:when test="$yFormat='CV'">
																		<B>%CV</B>
																	</xsl:when>
																	<xsl:when test="$yFormat='SDN'">
																		<xsl:if test="position()=2"><B>SD</B></xsl:if>
																		<xsl:if test="position()=3"><B>N</B></xsl:if>
																	</xsl:when>
																	<xsl:when test="$yFormat='SEN'">
																		<xsl:if test="position()=2"><B>SEM</B></xsl:if>
																		<xsl:if test="position()=3"><B>N</B></xsl:if>
																	</xsl:when>
																	<xsl:when test="$yFormat='CVN'">
																		<xsl:if test="position()=2"><B>%CV</B></xsl:if>
																		<xsl:if test="position()=3"><B>N</B></xsl:if>
																	</xsl:when>
																	<xsl:when test="$yFormat='low-high'">
																		<xsl:if test="position()=2"><B>+Error</B></xsl:if>
																		<xsl:if test="position()=3"><B>-Error</B></xsl:if>
																	</xsl:when>
																	<xsl:when test="$yFormat='upper-lower-limits'">
																		<xsl:if test="position()=2"><B>UpperLimit</B></xsl:if>
																		<xsl:if test="position()=3"><B>LowerLimit</B></xsl:if>
																	</xsl:when>
																</xsl:choose>
															</xsl:if>
														</xsl:if>
													</xsl:if>
												</xsl:if>
					 				
	   			</TD>
										</xsl:for-each>
									</xsl:for-each>
								</xsl:if>
							</TR>
						</xsl:if>
						<TR>
							<xsl:for-each select="ps:RowTitlesColumn/ps:Subcolumn">
								<td valign="top" nowrap="1">
									<xsl:attribute name="width"><xsl:value-of select="$width"/>%</xsl:attribute>
									<xsl:apply-templates select="ps:d"/>
									<xsl:if test="count(ps:d)=0"><br/></xsl:if>
								</td>
							</xsl:for-each>
							<xsl:if test="not($isXAdvancedColumn)">
								<xsl:for-each select="ps:XColumn/ps:Subcolumn">
									<td valign="top" nowrap="1">
										<xsl:attribute name="width"><xsl:value-of select="$width"/>%</xsl:attribute>
										<xsl:apply-templates select="ps:d"/>
										<xsl:if test="count(ps:d)=0"><br/></xsl:if>
									</td>
								</xsl:for-each>
							</xsl:if>
							<xsl:if test="$isXAdvancedColumn">
								<xsl:for-each select="ps:XAdvancedColumn/ps:Subcolumn">
									<td valign="top" nowrap="1">
										<xsl:attribute name="width"><xsl:value-of select="$width"/>%</xsl:attribute>
										<xsl:apply-templates select="ps:d"/>
										<xsl:if test="count(ps:d)=0"><br/></xsl:if>
									</td>
								</xsl:for-each>
							</xsl:if>
							<xsl:for-each select="ps:YColumn/ps:Subcolumn">
								<td valign="top" nowrap="1">
									<xsl:attribute name="width"><xsl:value-of select="$width"/>%</xsl:attribute>
									<xsl:apply-templates select="ps:d"/>
									<xsl:if test="count(ps:d)=0"><br/></xsl:if>
								</td>
							</xsl:for-each>
						</TR>
					</TABLE>
					<br/>
					<xsl:apply-templates select="ps:Info"/>
					<br/>
					<hr/>
					<br/>
				</xsl:for-each>
			</BODY>
		</HTML>
	</xsl:template>
	<!--<xsl:template match="ps:d">
	<xsl:choose>
		<xsl:when test="@Excluded and string-length(text())" >
		    <font color="#0000ee">
	      		<xsl:apply-templates select="node()"/>*		
		    </font>
		</xsl:when>
	    <xsl:otherwise>
			<xsl:apply-templates select="node()"/>
		</xsl:otherwise>
	</xsl:choose>
	<xsl:if test="position()!=last()"><br/></xsl:if>
</xsl:template>-->
	<xsl:template match="ps:d">
		<xsl:choose>
			<xsl:when test="@Excluded and string-length(text())">
				<xsl:choose>
					<xsl:when test="../../../@EVFormat='AsteriskAfterNumber'">
						<font color="#0000ee">
							<xsl:apply-templates select="node()"/>*
						</font>
					</xsl:when>
					<xsl:when test="../../../@EVFormat='Blank'">
					</xsl:when>
					<xsl:otherwise>
						<xsl:apply-templates select="node()"/>
					</xsl:otherwise>
				</xsl:choose>
			</xsl:when>
			<xsl:otherwise>
				<xsl:apply-templates select="node()"/>
			</xsl:otherwise>
		</xsl:choose>
		<xsl:if test="position()!=last()"><br/></xsl:if>
	</xsl:template>
	<xsl:template match="ps:Title">
		<b><xsl:apply-templates select="node()"/></b>
	</xsl:template>
	<xsl:template match="node()">
		<xsl:param name="DoNotUseBR" select="0"/>
		<xsl:choose>
			<xsl:when test="name()='BR' and $DoNotUseBR=1">
				<div/>
			</xsl:when>
			<xsl:otherwise>
				<xsl:copy>
					<xsl:apply-templates select="node()">
						<xsl:with-param name="DoNotUseBR" select="$DoNotUseBR"/>
					</xsl:apply-templates>
				</xsl:copy>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:template>
	<xsl:template match="ps:Font">
		<xsl:param name="DoNotUseBR" select="0"/>
		<font>
			<xsl:if test="@Size">
				<xsl:attribute name="STYLE">font-size:<xsl:value-of select="@Size"/>pt</xsl:attribute>
			</xsl:if>
			<xsl:copy-of select="@*"/>
			<xsl:apply-templates select="node()">
				<xsl:with-param name="DoNotUseBR" select="$DoNotUseBR"/>
			</xsl:apply-templates>
		</font>
	</xsl:template>
	<xsl:template match="ps:LineStyles">
		<div>
			<xsl:if test="@align">
				<xsl:attribute name="align"><xsl:value-of select="@align"/></xsl:attribute>
			</xsl:if>
			<xsl:if test="@spacing">
				<xsl:variable name="sp" select="@spacing"/>
				<xsl:attribute name="style">margin-bottom:<xsl:value-of select="(@spacing -100) div 5"/>pt</xsl:attribute>
			</xsl:if>
			<xsl:apply-templates select="node()">
				<xsl:with-param name="DoNotUseBR" select="1"/>
			</xsl:apply-templates>
		</div>
	</xsl:template>
	<xsl:template match="ps:Notes">
		<xsl:apply-templates select="node()">
			<xsl:with-param name="DoNotUseBR" select="1"/>
		</xsl:apply-templates>
	</xsl:template>
	<xsl:template match="/">
		<HTML>
			<BODY>
				<xsl:apply-templates select="//ps:GraphPadPrismFile"/>
			</BODY>
		</HTML>
	</xsl:template>
</xsl:stylesheet>
