﻿<?xml version="1.0" encoding="utf-8"?>
<Package xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10" xmlns:mp="http://schemas.microsoft.com/appx/2014/phone/manifest" xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10" xmlns:rescap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/restrictedcapabilities" xmlns:genTemplate="http://schemas.microsoft.com/appx/developer/templatestudio" xmlns:com="http://schemas.microsoft.com/appx/manifest/com/windows10" xmlns:desktop="http://schemas.microsoft.com/appx/manifest/desktop/windows10" IgnorableNamespaces="uap rescap genTemplate build" xmlns:build="http://schemas.microsoft.com/developer/appx/2015/build">
  <!--
    THIS PACKAGE MANIFEST FILE IS GENERATED BY THE BUILD PROCESS.

    Changes to this file will be lost when it is regenerated. To correct errors in this file, edit the source .appxmanifest file.

    For more information on package manifest files, see http://go.microsoft.com/fwlink/?LinkID=241727
  -->
  <Identity Name="96ba052f-0948-44d8-86c4-a0212e4ae047" Publisher="CN=&quot;Open Source Developer, Benjamin Thomas&quot;, O=Open Source Developer, L=Gloucester, S=Gloucestershire, C=GB" Version="5.2.3.0" ProcessorArchitecture="x64" />
  <mp:PhoneIdentity PhoneProductId="96ba052f-0948-44d8-86c4-a0212e4ae047" PhonePublisherId="00000000-0000-0000-0000-000000000000" />
  <Properties>
    <DisplayName>VRCFaceTracking</DisplayName>
    <PublisherDisplayName>benaclejames</PublisherDisplayName>
    <Logo>Assets\StoreLogo.png</Logo>
  </Properties>
  <Dependencies>
    <TargetDeviceFamily Name="Windows.Universal" MinVersion="10.0.17763.0" MaxVersionTested="10.0.19041.0" />
    <TargetDeviceFamily Name="Windows.Desktop" MinVersion="10.0.19041.0" MaxVersionTested="10.0.19041.0" />
    <PackageDependency Name="Microsoft.VCLibs.140.00.UWPDesktop" MinVersion="14.0.24217.0" Publisher="CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US" />
  </Dependencies>
  <Resources>
    <Resource Language="EN" />
    <Resource Language="ZH-CN" />
    <Resource Language="PL" />
    <Resource Language="ES" />
  </Resources>
  <Applications>
    <Application Id="App" Executable="VRCFaceTracking.exe" EntryPoint="Windows.FullTrustApplication">
      <uap:VisualElements DisplayName="VRCFaceTracking" BackgroundColor="transparent" Square150x150Logo="Assets\Square150x150Logo.png" Square44x44Logo="Assets\Square44x44Logo.png" Description="VRChat OSC App to allow AV3 Avatars to interact with eye and lip tracking runtimes">
        <uap:DefaultTile Wide310x150Logo="Assets\Wide310x150Logo.png" Square71x71Logo="Assets\SmallTile.png" Square310x310Logo="Assets\LargeTile.png" />
        <uap:SplashScreen Image="Assets\SplashScreen.png" />
        <uap:LockScreen BadgeLogo="Assets\BadgeLogo.png" Notification="badgeAndTileText" />
      </uap:VisualElements>
      <Extensions>
        <desktop:Extension Category="windows.toastNotificationActivation">
          <desktop:ToastNotificationActivation ToastActivatorCLSID="32c2214e-7dbf-462f-9ee1-74fadd80d026" />
        </desktop:Extension>
        <com:Extension Category="windows.comServer">
          <com:ComServer>
            <com:ExeServer Executable="VRCFaceTracking.exe" Arguments="----AppNotificationActivated:" DisplayName="Toast activator">
              <com:Class Id="32c2214e-7dbf-462f-9ee1-74fadd80d026" DisplayName="Toast activator" />
            </com:ExeServer>
          </com:ComServer>
        </com:Extension>
      </Extensions>
    </Application>
  </Applications>
  <Capabilities>
    <rescap:Capability Name="runFullTrust" />
    <Capability Name="internetClientServer" />
  </Capabilities>
  <genTemplate:Metadata>
    <genTemplate:Item Name="generator" Value="Template Studio" />
    <genTemplate:Item Name="wizardVersion" Version="v5.3" />
    <genTemplate:Item Name="projectType" Value="NavView" />
    <genTemplate:Item Name="framework" Value="MVVMToolkit" />
    <genTemplate:Item Name="platform" Value="WinUI" />
    <genTemplate:Item Name="appmodel" Value="Desktop" />
  </genTemplate:Metadata>
  <build:Metadata>
    <build:Item Name="Microsoft.UI.Xaml.Markup.Compiler.dll" Version="1.0.0.0" />
    <build:Item Name="makepri.exe" Version="10.0.22621.1 (WinBuild.160101.0800)" />
  </build:Metadata>
</Package>