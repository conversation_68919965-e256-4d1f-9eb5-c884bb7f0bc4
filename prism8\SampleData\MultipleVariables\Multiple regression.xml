<?xml version="1.0" encoding="UTF-8"?>

<?xml-stylesheet type="text/xsl" href="#"?>

<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:dt="urn:schemas-microsoft-com:datatypes" xmlns:ps="http://graphpad.com/prism/Prism.htm" version="1.0">

	<!--

	XML style sheet template for formatting data and info tables from GraphPad Prism 8.0.

	Copyright 1992-2016 GraphPad Software, Inc.

-->

	<xsl:output method="html" version="4.0" omit-xml-declaration="yes"/>

	<GraphPadPrismFile xmlns="http://graphpad.com/prism/Prism.htm" PrismXMLVersion="5.00">

<Created>

<OriginalVersion CreatedByProgram="GraphPad Prism" CreatedByVersion="********" Login="Harvey" DateTime="2017-01-06T07:13:20-08:00" MinVersion="*******"/>

</Created>

<DefGraphButton ButtonID="101"/>

<InfoSequence>

</InfoSequence>

<TableSequence Selected="1">



<Ref ID="Table0" Selected="1"/>

</TableSequence>

<Table ID="Table0" XFormat="none" TableType="OneWay" ExtTableType="MultipleVariables" EVFormat="AsteriskAfterNumber">

<Title>Data 1</Title>

<FloatingNote ID="Sticky1" Auto="0" Color="Blue" Left="1348" Top="96" Width="754" Height="460" ScrWidth="1440" ScrHeight="900" ScrDPI="192">

<WebLink Flags="0" ToolTip="" URL="http://biostat.mc.vanderbilt.edu/wiki/Main/DataSets">

Vanderbilt data sets

</WebLink>



<B><Font Size="10" Color="#000000" Face="Arial">

Data source:

</Font></B>

<Font Size="10" Color="#000000" Face="Arial">

<BR/><BR/>This is a subset of columns from the Diabetes data set, part of a collection of sample data sets maintained by Vanderbilt (link below). The diabetes data were provided to that collection by of Dr. John Schorling, from his study Schorling JB, Roach J, Siegel M, Baturka N, Hunt DE, Guterbock TM, Stewart HL: 

</Font>

<I><Font Size="10" Color="#000000" Face="Arial">

A trial of church-based smoking cessation interventions for rural African Americans

</Font></I>

<Font Size="10" Color="#000000" Face="Arial">

. Preventive Medicine 26:92-101; 1997.

</Font>

</FloatingNote>



<FloatingNote ID="Sticky3" Auto="0" Color="Yellow" Left="456" Top="132" Width="866" Height="644" ScrWidth="1440" ScrHeight="900" ScrDPI="192">

<WebLink Flags="0" ToolTip="" URL="@help:REG_Enter-data-for-multiple-regres">

Learn more about multiple regression 

</WebLink>



<B><Font Size="11" Color="#000000" Face="Arial">

How the data are arranged

</Font></B>

<Font Size="11" Color="#000000" Face="Arial">

<BR/>Each row represents a different person. Each column is a different variable measured in that person. 

</Font>

<B><Font Size="11" Color="#000000" Face="Arial">

<BR/><BR/>The goal

</Font></B>

<Font Size="11" Color="#000000" Face="Arial">

<BR/>To use multiple regression to find a model that predicts the glycosylated hemoglobin from the other variables. 

</Font>

<B><Font Size="11" Color="#000000" Face="Arial">

<BR/><BR/>To run multiple regression

</Font></B>

<Font Size="11" Color="#000000" Face="Arial">

<BR/>1. Click Analyze.<BR/>2. Choose multiple  regression from the list of analyses for multiple variable data.<BR/>3. All the default settings are ok to begin with. <BR/>4. Look at the results table.<BR/>5. Look at the graph of predicted vs. actual Y values. Y is the dependent variable, usually the first column. 

</Font>

</FloatingNote>



<FloatingNote ID="Sticky6" Auto="0" Color="Green" Left="1334" Top="594" Width="672" Height="494" ScrWidth="1440" ScrHeight="900" ScrDPI="192">



<B><Font Size="10" Color="#000000" Face="Arial">

What is glydosylated hemoglobin?<BR/>

</Font></B>

<Font Size="10" Color="#000000" Face="Arial">

<BR/>The level of glycosylated hemoglobin is increased in the red blood cells of persons with poorly controlled diabetes mellitus. Its level is proportional to  the average blood glucose level over the past 3 months.<BR/><BR/>The normal level for glycosylated hemoglobin is less than 7%. <BR/><BR/>Glycosylated hemoglobin is also known as 

</Font>

<I><Font Size="10" Color="#000000" Face="Arial">

glycohemoglobin 

</Font></I>

<Font Size="10" Color="#000000" Face="Arial">

or as

</Font>

<I><Font Size="10" Color="#000000" Face="Arial">

 hemoglobin A1C

</Font></I>

<Font Size="10" Color="#000000" Face="Arial">

.

</Font>

</FloatingNote>



<YColumn Width="275" Decimals="9" Subcolumns="1">

<Title>Glycosylated hemoglobin %</Title>

<Subcolumn>

<d>4.309999943</d>

<d>4.440000057</d>

<d>4.639999866</d>

<d>4.630000114</d>

<d>7.71999979</d>

<d>4.809999943</d>

<d>4.840000153</d>

<d>3.940000057</d>

<d>4.840000153</d>

<d>5.78000021</d>

<d>4.769999981</d>

<d>4.96999979</d>

<d>4.46999979</d>

<d>4.590000153</d>

<d>4.670000076</d>

<d>3.410000086</d>

<d>4.329999924</d>

<d>4.53000021</d>

<d>5.28000021</d>

<d>11.23999977</d>

<d>6.489999771</d>

<d>4.670000076</d>

<d>12.73999977</d>

<d>5.559999943</d>

<d>4.610000134</d>

<d>4.179999828</d>

<d>5.099999905</d>

<d>4.28000021</d>

<d>4.519999981</d>

<d>4.369999886</d>

<d>5.110000134</d>

<d>4.46999979</d>

<d>15.52000046</d>

<d>5.659999847</d>

<d>3.670000076</d>

<d>4.03000021</d>

<d>2.680000067</d>

<d>3.559999943</d>

<d>6.210000038</d>

<d>7.909999847</d>

<d>4.579999924</d>

<d>3.890000105</d>

<d>4.380000114</d>

<d/>

<d>5.960000038</d>

<d>4.409999847</d>

<d>6.139999866</d>

<d>10.89999962</d>

<d>6.139999866</d>

<d>5.570000172</d>

<d>4.25</d>

<d>5.349999905</d>

<d>6.329999924</d>

<d>4.559999943</d>

<d>9.390000343</d>

<d>6.349999905</d>

<d>5.199999809</d>

<d>4.980000019</d>

<d>13.69999981</d>

<d/>

<d>10.93000031</d>

<d>5.230000019</d>

<d>14.31000042</d>

<d>3.99000001</d>

<d/>

<d>4.269999981</d>

<d>4.25</d>

<d>11.40999985</d>

<d>4.440000057</d>

<d>8.399999619</d>

<d>4.590000153</d>

<d>5.230000019</d>

<d>4.389999866</d>

<d>5.199999809</d>

<d>6.110000134</d>

<d>7.440000057</d>

<d>5.46999979</d>

<d>4.289999962</d>

<d>3.930000067</d>

<d>6.960000038</d>

<d>4.840000153</d>

<d>5.179999828</d>

<d>5.519999981</d>

<d>4.380000114</d>

<d>5.28000021</d>

<d>4.820000172</d>

<d>10.55000019</d>

<d>4.860000134</d>

<d>4.860000134</d>

<d>4.110000134</d>

<d>5.019999981</d>

<d>9.170000076</d>

<d>5.110000134</d>

<d>5.070000172</d>

<d>4.840000153</d>

<d>4.099999905</d>

<d>4.789999962</d>

<d>7.789999962</d>

<d>5.150000095</d>

<d>10.14999962</d>

<d>4.170000076</d>

<d>4.050000191</d>

<d>5.440000057</d>

<d>4.309999943</d>

<d>9.770000458</d>

<d>3.890000105</d>

<d>5.170000076</d>

<d>5.710000038</d>

<d>7.869999886</d>

<d/>

<d>4.659999847</d>

<d>4.21999979</d>

<d>5.170000076</d>

<d>3.75999999</d>

<d>4.309999943</d>

<d>4.610000134</d>

<d/>

<d/>

<d>5.349999905</d>

<d>4.360000134</d>

<d>4.409999847</d>

<d>8.449999809</d>

<d>3.980000019</d>

<d>9.760000229</d>

<d>4.829999924</d>

<d>4.010000229</d>

<d>3.839999914</d>

<d>4.949999809</d>

<d>6.389999866</d>

<d>7.53000021</d>

<d>4.619999886</d>

<d>4.789999962</d>

<d>5.090000153</d>

<d>6.510000229</d>

<d>4.860000134</d>

<d>4.409999847</d>

<d>6.130000114</d>

<d>6.489999771</d>

<d>7.510000229</d>

<d>6.96999979</d>

<d>4.900000095</d>

<d>4.809999943</d>

<d>4.579999924</d>

<d>9.180000305</d>

<d>5.460000038</d>

<d>4.039999962</d>

<d>5.230000019</d>

<d>6.340000153</d>

<d>5.369999886</d>

<d>4.510000229</d>

<d>9.579999924</d>

<d>5.550000191</d>

<d>5.599999905</d>

<d>4.869999886</d>

<d>5.599999905</d>

<d>12.97000027</d>

<d>5.630000114</d>

<d>4.5</d>

<d>5.559999943</d>

<d>4.03000021</d>

<d>4.380000114</d>

<d>13.60000038</d>

<d>4.570000172</d>

<d>4.659999847</d>

<d>4.710000038</d>

<d>4.400000095</d>

<d>9.25</d>

<d>4.739999771</d>

<d>4.400000095</d>

<d>5.489999771</d>

<d>3.440000057</d>

<d>9.819999695</d>

<d>4.960000038</d>

<d>11.59000015</d>

<d>4.409999847</d>

<d>4.440000057</d>

<d>7.139999866</d>

<d>8.81000042</d>

<d>5.349999905</d>

<d>4.690000057</d>

<d>7.400000095</d>

<d>4.730000019</d>

<d>4.519999981</d>

<d>4.949999809</d>

<d>4.210000038</d>

<d>4.559999943</d>

<d>5.349999905</d>

<d>4.039999962</d>

<d>5.489999771</d>

<d>4.639999866</d>

<d>4.400000095</d>

<d>5.239999771</d>

<d/>

<d>4.03000021</d>

<d>4.809999943</d>

<d>4.309999943</d>

<d>5.230000019</d>

<d>5.21999979</d>

<d>8.25</d>

<d>4.949999809</d>

<d>4.010000229</d>

<d>4.670000076</d>

<d>6.170000076</d>

<d>4.760000229</d>

<d>4.659999847</d>

<d>4.820000172</d>

<d>4.96999979</d>

<d>5.5</d>

<d>3.549999952</d>

<d>10.09000015</d>

<d>4.010000229</d>

<d>5.099999905</d>

<d>12.67000008</d>

<d>12.06999969</d>

<d>4.170000076</d>

<d>4.75</d>

<d>3.549999952</d>

<d/>

<d>4.440000057</d>

<d>4.920000076</d>

<d>4.590000153</d>

<d>4.730000019</d>

<d>5.139999866</d>

<d>5.739999771</d>

<d>4.869999886</d>

<d>5.409999847</d>

<d>5.130000114</d>

<d>4.639999866</d>

<d>6.960000038</d>

<d>5.360000134</d>

<d>5.53000021</d>

<d>5.380000114</d>

<d>4.260000229</d>

<d>4.340000153</d>

<d>8.569999695</d>

<d>5.019999981</d>

<d>4.360000134</d>

<d>3.329999924</d>

<d>4.179999828</d>

<d>4.78000021</d>

<d>4.739999771</d>

<d>3.970000029</d>

<d>6.420000076</d>

<d>6.480000019</d>

<d>5.599999905</d>

<d>4.849999905</d>

<d>4.610000134</d>

<d>4.099999905</d>

<d>7.510000229</d>

<d>4.239999771</d>

<d>4.760000229</d>

<d>3.75</d>

<d>5.039999962</d>

<d>5.340000153</d>

<d>4.5</d>

<d>4.369999886</d>

<d>7.480000019</d>

<d>4.360000134</d>

<d>4.739999771</d>

<d>5.260000229</d>

<d>10.47000027</d>

<d>5.170000076</d>

<d>5.699999809</d>

<d>3.589999914</d>

<d>6.420000076</d>

<d>5.03000021</d>

<d>4.409999847</d>

<d>5.679999828</d>

<d>4.389999866</d>

<d>4.070000172</d>

<d>4.619999886</d>

<d>3.700000048</d>

<d>3.960000038</d>

<d/>

<d>4.75</d>

<d>4.309999943</d>

<d>5.349999905</d>

<d>4.800000191</d>

<d>4.809999943</d>

<d>4.880000114</d>

<d>5.050000191</d>

<d>4.869999886</d>

<d>4.670000076</d>

<d>4.429999828</d>

<d>4.269999981</d>

<d>5.03000021</d>

<d>4.400000095</d>

<d>4.670000076</d>

<d>4.210000038</d>

<d>5.239999771</d>

<d>4.610000134</d>

<d>4.400000095</d>

<d>5.010000229</d>

<d>6.059999943</d>

<d>10.75</d>

<d>4.690000057</d>

<d>5.630000114</d>

<d>3.690000057</d>

<d>3.849999905</d>

<d>4.090000153</d>

<d>4.099999905</d>

<d>9.279999733</d>

<d>3.880000114</d>

<d>5.699999809</d>

<d>2.849999905</d>

<d>13.06000042</d>

<d>3.970000029</d>

<d>3.029999971</d>

<d>5.510000229</d>

<d>5.679999828</d>

<d>4.199999809</d>

<d>6.440000057</d>

<d>9.619999886</d>

<d>4.539999962</d>

<d>7.510000229</d>

<d>5.630000114</d>

<d>5.769999981</d>

<d>4.559999943</d>

<d>4.400000095</d>

<d>4.699999809</d>

<d>2.849999905</d>

<d>5.340000153</d>

<d>4.949999809</d>

<d>3.589999914</d>

<d>8.229999542</d>

<d>5.059999943</d>

<d/>

<d/>

<d>8.06000042</d>

<d>4.900000095</d>

<d>4.710000038</d>

<d>3.799999952</d>

<d>4.670000076</d>

<d>10.97000027</d>

<d>4.429999828</d>

<d>5.909999847</d>

<d>2.730000019</d>

<d>5.119999886</d>

<d>3.779999971</d>

<d>5.119999886</d>

<d>3.619999886</d>

<d>4.659999847</d>

<d>5.010000229</d>

<d>3.75</d>

<d>4.550000191</d>

<d>4.920000076</d>

<d>4.090000153</d>

<d>5.579999924</d>

<d>3.980000019</d>

<d>7.21999979</d>

<d>3.970000029</d>

<d>4.650000095</d>

<d>4.829999924</d>

<d>4.880000114</d>

<d>4.929999828</d>

<d>5.159999847</d>

<d>6.78000021</d>

<d>4.159999847</d>

<d>12.15999985</d>

<d>4.28000021</d>

<d>4.159999847</d>

<d>5.21999979</d>

<d>14.93999958</d>

<d>10.15999985</d>

<d>10.06999969</d>

<d>3.660000086</d>

<d>6.480000019</d>

<d>4.900000095</d>

<d>11.18000031</d>

<d>4.329999924</d>

<d>3.75</d>

<d>4.96999979</d>

<d>4.039999962</d>

<d>4.679999828</d>

<d>4.170000076</d>

<d>4.139999866</d>

<d>4.980000019</d>

<d>4.659999847</d>

<d>4.289999962</d>

<d>5.559999943</d>

<d>4.449999809</d>

<d>11.40999985</d>

<d>4.070000172</d>

<d>5.010000229</d>

<d>4.480000019</d>

<d>4.650000095</d>

<d>4.050000191</d>

<d>8.109999657</d>

<d>4.130000114</d>

<d>3.579999924</d>

<d>9.369999886</d>

<d>5.53000021</d>

<d>4.960000038</d>

<d>4.380000114</d>

<d>4.820000172</d>

<d>4.989999771</d>

<d/>

<d>4.28000021</d>

<d>16.11000061</d>

<d>4.389999866</d>

<d>13.63000011</d>

<d>4.489999771</d>

</Subcolumn>

</YColumn>

<YColumn Width="167" Decimals="0" Subcolumns="1">

<Title>Total cholesterol</Title>

<Subcolumn>

<d>203</d>

<d>165</d>

<d>228</d>

<d>78</d>

<d>249</d>

<d>248</d>

<d>195</d>

<d>227</d>

<d>177</d>

<d>263</d>

<d>242</d>

<d>215</d>

<d>238</d>

<d>183</d>

<d>191</d>

<d>213</d>

<d>255</d>

<d>230</d>

<d>194</d>

<d>196</d>

<d>186</d>

<d>234</d>

<d>203</d>

<d>281</d>

<d>228</d>

<d>179</d>

<d>232</d>

<d/>

<d>254</d>

<d>215</d>

<d>177</d>

<d>182</d>

<d>265</d>

<d>182</d>

<d>199</d>

<d>183</d>

<d>194</d>

<d>190</d>

<d>173</d>

<d>182</d>

<d>136</d>

<d>218</d>

<d>225</d>

<d>262</d>

<d>213</d>

<d>243</d>

<d>148</d>

<d>128</d>

<d>169</d>

<d>157</d>

<d>196</d>

<d>237</d>

<d>212</d>

<d>233</d>

<d>289</d>

<d>193</d>

<d>204</d>

<d>165</d>

<d>237</d>

<d>218</d>

<d>296</d>

<d>178</d>

<d>443</d>

<d>145</d>

<d>234</d>

<d>146</d>

<d>223</d>

<d>213</d>

<d>173</d>

<d>232</d>

<d>171</d>

<d>164</d>

<d>170</d>

<d>180</d>

<d>204</d>

<d>209</d>

<d>242</d>

<d>134</d>

<d>217</d>

<d>251</d>

<d>217</d>

<d>300</d>

<d>218</d>

<d>189</d>

<d>185</d>

<d>206</d>

<d>218</d>

<d>189</d>

<d>229</d>

<d>228</d>

<d>159</d>

<d>249</d>

<d>170</d>

<d>174</d>

<d>204</d>

<d>203</d>

<d>241</d>

<d>245</d>

<d>143</d>

<d>224</d>

<d>168</d>

<d>184</d>

<d>199</d>

<d>158</d>

<d>209</d>

<d>214</d>

<d>293</d>

<d>227</d>

<d>292</d>

<d>218</d>

<d>244</d>

<d>283</d>

<d>186</d>

<d>273</d>

<d>193</d>

<d>194</d>

<d>231</d>

<d>217</d>

<d>174</d>

<d>225</d>

<d>268</d>

<d>195</d>

<d>179</d>

<d>215</d>

<d>185</d>

<d>132</d>

<d>175</d>

<d>179</d>

<d>228</d>

<d>181</d>

<d>160</d>

<d>188</d>

<d>168</d>

<d>318</d>

<d>192</d>

<d>209</d>

<d>129</d>

<d>160</d>

<d>160</d>

<d>211</d>

<d>262</d>

<d>201</d>

<d>263</d>

<d>219</d>

<d>191</d>

<d>171</d>

<d>219</d>

<d>347</d>

<d>269</d>

<d>164</d>

<d>181</d>

<d>190</d>

<d>255</d>

<d>218</d>

<d>223</d>

<d>254</d>

<d>236</d>

<d>176</d>

<d>158</d>

<d>181</d>

<d>151</d>

<d>115</d>

<d>271</d>

<d>190</d>

<d>118</d>

<d>168</d>

<d>254</d>

<d>193</d>

<d>187</d>

<d>212</d>

<d>170</d>

<d>215</d>

<d>199</d>

<d>140</d>

<d>216</d>

<d>204</d>

<d>193</d>

<d>267</d>

<d>201</d>

<d>204</d>

<d>246</d>

<d>229</d>

<d>172</d>

<d>197</d>

<d>205</d>

<d>219</d>

<d>174</d>

<d>192</d>

<d>206</d>

<d>160</d>

<d>216</d>

<d>236</d>

<d>205</d>

<d>206</d>

<d>143</d>

<d>173</d>

<d>235</d>

<d>169</d>

<d>283</d>

<d>174</d>

<d>271</d>

<d>203</d>

<d>188</d>

<d>293</d>

<d>215</d>

<d>207</d>

<d>179</d>

<d>202</d>

<d>211</d>

<d>211</d>

<d>151</d>

<d>171</d>

<d>342</d>

<d>179</d>

<d>155</d>

<d>197</d>

<d>200</d>

<d>237</d>

<d>198</d>

<d>240</d>

<d>192</d>

<d>145</d>

<d>269</d>

<d>240</d>

<d>205</d>

<d>266</d>

<d>188</d>

<d>222</d>

<d>142</d>

<d>268</d>

<d>174</d>

<d>214</d>

<d>194</d>

<d>196</d>

<d>207</d>

<d>204</d>

<d>189</d>

<d>179</d>

<d>159</d>

<d>260</d>

<d>228</d>

<d>242</d>

<d>227</d>

<d>208</d>

<d>208</d>

<d>209</d>

<d>163</d>

<d>201</d>

<d>237</d>

<d>176</d>

<d>146</d>

<d>231</d>

<d>241</d>

<d>305</d>

<d>149</d>

<d>183</d>

<d>235</d>

<d>244</d>

<d>199</d>

<d>224</d>

<d>173</d>

<d>192</d>

<d>157</d>

<d>172</d>

<d>170</d>

<d>215</d>

<d>214</d>

<d>195</d>

<d>230</d>

<d>206</d>

<d>147</d>

<d>234</d>

<d>135</d>

<d>226</d>

<d>179</d>

<d>163</d>

<d>191</d>

<d>138</d>

<d>184</d>

<d>181</d>

<d>224</d>

<d>293</d>

<d>147</d>

<d>198</d>

<d>152</d>

<d>277</d>

<d>219</d>

<d>182</d>

<d>135</d>

<d>277</d>

<d>212</d>

<d>162</d>

<d>207</d>

<d>255</d>

<d>404</d>

<d>239</d>

<d>220</d>

<d>165</d>

<d>243</d>

<d>149</d>

<d>178</d>

<d>190</d>

<d>226</d>

<d>132</d>

<d>160</d>

<d>204</d>

<d>164</d>

<d>155</d>

<d>251</d>

<d>198</d>

<d>179</d>

<d>223</d>

<d>207</d>

<d>244</d>

<d>245</d>

<d>191</d>

<d>221</d>

<d>300</d>

<d>173</d>

<d>138</d>

<d>203</d>

<d>260</d>

<d>166</d>

<d>180</d>

<d>159</d>

<d>207</d>

<d>298</d>

<d>203</d>

<d>191</d>

<d>231</d>

<d>184</d>

<d>164</d>

<d>134</d>

<d>220</d>

<d>180</d>

<d>216</d>

<d>158</d>

<d>261</d>

<d>172</d>

<d>249</d>

<d>189</d>

<d>225</d>

<d>193</d>

<d>219</d>

<d>156</d>

<d>224</d>

<d>181</d>

<d>306</d>

<d>122</d>

<d>219</d>

<d>150</d>

<d>185</d>

<d>226</d>

<d>206</d>

<d>199</d>

<d>239</d>

<d>235</d>

<d>184</d>

<d>242</d>

<d>307</d>

<d>204</d>

<d>212</d>

<d>203</d>

<d>219</d>

<d>226</d>

<d>217</d>

<d>157</d>

<d>235</d>

<d>252</d>

<d>204</d>

<d>188</d>

<d>194</d>

<d>215</d>

<d>179</d>

<d>202</d>

<d>194</d>

<d>227</d>

<d>337</d>

<d>255</d>

<d>162</d>

<d>322</d>

<d>289</d>

<d>217</d>

<d>209</d>

<d>214</d>

<d>302</d>

<d>179</d>

<d>279</d>

<d>144</d>

<d>270</d>

<d>196</d>

<d>221</d>

<d>210</d>

<d>192</d>

<d>169</d>

<d>179</d>

<d>216</d>

<d>301</d>

<d>296</d>

<d>284</d>

<d>194</d>

<d>199</d>

<d>159</d>

</Subcolumn>

</YColumn>

<YColumn Width="140" Decimals="0" Subcolumns="1">

<Title>Glucose</Title>

<Subcolumn>

<d>82</d>

<d>97</d>

<d>92</d>

<d>93</d>

<d>90</d>

<d>94</d>

<d>92</d>

<d>75</d>

<d>87</d>

<d>89</d>

<d>82</d>

<d>128</d>

<d>75</d>

<d>79</d>

<d>76</d>

<d>83</d>

<d>78</d>

<d>112</d>

<d>81</d>

<d>206</d>

<d>97</d>

<d>65</d>

<d>299</d>

<d>92</d>

<d>66</d>

<d>80</d>

<d>87</d>

<d>74</d>

<d>84</d>

<d>72</d>

<d>101</d>

<d>85</d>

<d>330</d>

<d>85</d>

<d>87</d>

<d>81</d>

<d>86</d>

<d>107</d>

<d>80</d>

<d>206</d>

<d>81</d>

<d>68</d>

<d>83</d>

<d>84</d>

<d>76</d>

<d>52</d>

<d>193</d>

<d>223</d>

<d>85</d>

<d>74</d>

<d>82</d>

<d>87</d>

<d>97</d>

<d>92</d>

<d>111</d>

<d>106</d>

<d>128</d>

<d>94</d>

<d>233</d>

<d>88</d>

<d>262</d>

<d>78</d>

<d>185</d>

<d>85</d>

<d>80</d>

<d>77</d>

<d>75</d>

<d>203</d>

<d>131</d>

<d>184</d>

<d>92</d>

<d>86</d>

<d>69</d>

<d>84</d>

<d>57</d>

<d>113</d>

<d>108</d>

<d>105</d>

<d>81</d>

<d>94</d>

<d>88</d>

<d>103</d>

<d>87</d>

<d>96</d>

<d>84</d>

<d>85</d>

<d>182</d>

<d>75</d>

<d>95</d>

<d>76</d>

<d>88</d>

<d>197</d>

<d>106</d>

<d>125</d>

<d>62</d>

<d>84</d>

<d>86</d>

<d>120</d>

<d>91</d>

<d>341</d>

<d>69</d>

<d>79</d>

<d>130</d>

<d>91</d>

<d>176</d>

<d>111</d>

<d>85</d>

<d>105</d>

<d>235</d>

<d>80</d>

<d>101</d>

<d>83</d>

<d>74</d>

<d>94</d>

<d>77</d>

<d>80</d>

<d>105</d>

<d>78</d>

<d>173</d>

<d>84</d>

<d>85</d>

<d>108</d>

<d>70</d>

<d>119</d>

<d>76</d>

<d>99</d>

<d>91</d>

<d>81</d>

<d>115</d>

<d>177</d>

<d>100</d>

<d>77</d>

<d>101</d>

<d>270</d>

<d>109</d>

<d>87</d>

<d>110</d>

<d>122</d>

<d>196</d>

<d>48</d>

<d>93</d>

<d>81</d>

<d>82</d>

<d>112</d>

<d>83</d>

<d>97</d>

<d>112</d>

<d>197</d>

<d>73</d>

<d>71</d>

<d>255</d>

<d>84</d>

<d>112</d>

<d>126</d>

<d>90</d>

<d>342</d>

<d>102</d>

<d>92</d>

<d>91</d>

<d>83</d>

<d>85</d>

<d>239</d>

<d>121</d>

<d>92</d>

<d>95</d>

<d>82</d>

<d>121</d>

<d>77</d>

<d>84</d>

<d>79</d>

<d>76</d>

<d>110</d>

<d>85</d>

<d>385</d>

<d>79</d>

<d>113</d>

<d>248</d>

<d>133</d>

<d>106</d>

<d>120</d>

<d>104</d>

<d>91</d>

<d>101</d>

<d>120</d>

<d>79</d>

<d>106</d>

<d>90</d>

<d>89</d>

<d>94</d>

<d>71</d>

<d>109</d>

<d>111</d>

<d>88</d>

<d>112</d>

<d>371</d>

<d>83</d>

<d>91</d>

<d>95</d>

<d>145</d>

<d>93</d>

<d>103</d>

<d>94</d>

<d>174</d>

<d>87</d>

<d>80</d>

<d>77</d>

<d>77</d>

<d>81</d>

<d>98</d>

<d>225</d>

<d>74</d>

<d>85</d>

<d>251</d>

<d>236</d>

<d>58</d>

<d>92</d>

<d>56</d>

<d>96</d>

<d>118</d>

<d>88</d>

<d>56</d>

<d>84</d>

<d>59</d>

<d>96</d>

<d>83</d>

<d>82</d>

<d>88</d>

<d>82</d>

<d>155</d>

<d>90</d>

<d>105</d>

<d>87</d>

<d>54</d>

<d>115</d>

<d>187</d>

<d>89</d>

<d>84</d>

<d>77</d>

<d>100</d>

<d>68</d>

<d>79</d>

<d>74</d>

<d>98</d>

<d>122</d>

<d>95</d>

<d>89</d>

<d>83</d>

<d>100</d>

<d>118</d>

<d>90</d>

<d>79</d>

<d>70</d>

<d>92</d>

<d>91</d>

<d>77</d>

<d>69</d>

<d>109</d>

<d>101</d>

<d>153</d>

<d>85</d>

<d>225</d>

<d>124</d>

<d>91</d>

<d>117</d>

<d>67</d>

<d>97</d>

<d>67</d>

<d>171</d>

<d>86</d>

<d>90</d>

<d>86</d>

<d>78</d>

<d>88</d>

<d>68</d>

<d>75</d>

<d>69</d>

<d>74</d>

<d>95</d>

<d>92</d>

<d>101</d>

<d>98</d>

<d>115</d>

<d>78</d>

<d>92</d>

<d>103</d>

<d>119</d>

<d>105</d>

<d>74</d>

<d>88</d>

<d>88</d>

<d>82</d>

<d>76</d>

<d>102</d>

<d>100</d>

<d>206</d>

<d>97</d>

<d>95</d>

<d>76</d>

<d>74</d>

<d>138</d>

<d>64</d>

<d>228</d>

<d>97</d>

<d>83</d>

<d>82</d>

<d>173</d>

<d>91</d>

<d>81</d>

<d>118</d>

<d>86</d>

<d>90</d>

<d>88</d>

<d>71</d>

<d>89</d>

<d>119</d>

<d>81</d>

<d>120</d>

<d>65</d>

<d>85</d>

<d>81</d>

<d>71</d>

<d>67</d>

<d>77</d>

<d>92</d>

<d>172</d>

<d>75</d>

<d>84</d>

<d>104</d>

<d>155</d>

<d>84</d>

<d>76</d>

<d>94</d>

<d>101</d>

<d>60</d>

<d>76</d>

<d>155</d>

<d>74</d>

<d>101</d>

<d>70</d>

<d>81</d>

<d>80</d>

<d>74</d>

<d>75</d>

<d>78</d>

<d>86</d>

<d>71</d>

<d>77</d>

<d>92</d>

<d>82</d>

<d>130</d>

<d>80</d>

<d>67</d>

<d>100</d>

<d>83</d>

<d>81</d>

<d>85</d>

<d>106</d>

<d>99</d>

<d>297</d>

<d>87</d>

<d>94</d>

<d>88</d>

<d>90</d>

<d>173</d>

<d>279</d>

<d>75</d>

<d>92</d>

<d>102</d>

<d>161</d>

<d>71</d>

<d>84</d>

<d>95</d>

<d>64</d>

<d>105</d>

<d>84</d>

<d>87</d>

<d>85</d>

<d>85</d>

<d>83</d>

<d>90</d>

<d>87</d>

<d>267</d>

<d>87</d>

<d>91</d>

<d>77</d>

<d>81</d>

<d>85</d>

<d>270</d>

<d>81</d>

<d>73</d>

<d>120</d>

<d>126</d>

<d>81</d>

<d>85</d>

<d>104</d>

<d>85</d>

<d>84</d>

<d>90</d>

<d>369</d>

<d>89</d>

<d>269</d>

<d>76</d>

<d>88</d>

</Subcolumn>

</YColumn>

<YColumn Width="140" Decimals="0" Subcolumns="1">

<Title>HDL</Title>

<Subcolumn>

<d>56</d>

<d>24</d>

<d>37</d>

<d>12</d>

<d>28</d>

<d>69</d>

<d>41</d>

<d>44</d>

<d>49</d>

<d>40</d>

<d>54</d>

<d>34</d>

<d>36</d>

<d>46</d>

<d>30</d>

<d>47</d>

<d>38</d>

<d>64</d>

<d>36</d>

<d>41</d>

<d>50</d>

<d>76</d>

<d>43</d>

<d>41</d>

<d>45</d>

<d>92</d>

<d>30</d>

<d/>

<d>52</d>

<d>42</d>

<d>36</d>

<d>43</d>

<d>34</d>

<d>37</d>

<d>63</d>

<d>60</d>

<d>67</d>

<d>32</d>

<d>57</d>

<d>43</d>

<d>51</d>

<d>46</d>

<d>42</d>

<d>38</d>

<d>40</d>

<d>59</d>

<d>14</d>

<d>24</d>

<d>51</d>

<d>47</d>

<d>58</d>

<d>41</d>

<d>45</d>

<d>39</d>

<d>50</d>

<d>63</d>

<d>61</d>

<d>69</d>

<d>58</d>

<d>39</d>

<d>60</d>

<d>59</d>

<d>23</d>

<d>29</d>

<d>63</d>

<d>60</d>

<d>85</d>

<d>75</d>

<d>69</d>

<d>114</d>

<d>54</d>

<d>40</d>

<d>64</d>

<d>69</d>

<d>74</d>

<d>65</d>

<d>53</d>

<d>42</d>

<d>60</d>

<d>36</d>

<d>40</d>

<d>44</d>

<d>38</d>

<d>47</d>

<d>52</d>

<d>46</d>

<d>54</d>

<d>72</d>

<d>74</d>

<d>53</d>

<d>43</d>

<d>44</d>

<d>42</d>

<d>44</d>

<d>70</d>

<d>75</d>

<d>63</d>

<d>39</d>

<d>37</d>

<d>33</d>

<d>45</d>

<d>39</d>

<d>48</d>

<d>48</d>

<d>55</d>

<d>59</d>

<d>94</d>

<d>44</d>

<d>55</d>

<d>71</d>

<d>36</d>

<d>74</d>

<d>76</d>

<d>49</d>

<d>49</d>

<d>34</d>

<d>61</d>

<d>48</d>

<d>34</d>

<d>82</d>

<d>51</d>

<d>46</d>

<d>52</d>

<d>44</d>

<d>58</d>

<d>34</d>

<d>42</d>

<d>35</d>

<d>61</d>

<d>24</d>

<d>36</d>

<d>45</d>

<d>59</d>

<d>108</d>

<d>44</d>

<d>34</d>

<d>42</d>

<d>41</d>

<d>33</d>

<d>34</d>

<d>43</d>

<d>87</d>

<d>92</d>

<d>73</d>

<d>88</d>

<d>69</d>

<d>73</d>

<d>42</d>

<d>34</d>

<d>63</d>

<d>26</d>

<d>44</d>

<d>34</d>

<d>32</d>

<d>48</d>

<d>37</d>

<d>36</d>

<d>55</d>

<d>31</d>

<d>44</d>

<d>48</d>

<d>36</d>

<d>40</d>

<d>44</d>

<d>39</d>

<d>44</d>

<d>39</d>

<d>45</d>

<d>64</d>

<d>49</d>

<d>60</d>

<d>36</d>

<d>59</d>

<d>31</d>

<d>46</d>

<d>35</d>

<d>24</d>

<d>34</d>

<d>53</d>

<d>44</d>

<d>62</d>

<d>43</d>

<d>46</d>

<d>37</d>

<d>32</d>

<d>50</d>

<d>36</d>

<d>30</d>

<d>44</d>

<d>44</d>

<d>86</d>

<d>82</d>

<d>41</d>

<d>33</d>

<d>46</d>

<d>37</d>

<d>37</d>

<d>29</d>

<d>39</d>

<d>77</d>

<d>90</d>

<d>62</d>

<d>24</d>

<d>120</d>

<d>100</d>

<d>46</d>

<d>72</d>

<d>55</d>

<d>40</d>

<d>29</d>

<d>47</d>

<d>61</d>

<d>48</d>

<d>63</d>

<d>69</d>

<d>46</d>

<d>51</d>

<d>52</d>

<d>46</d>

<d>49</d>

<d>42</d>

<d>54</d>

<d>66</d>

<d>57</d>

<d>42</d>

<d>54</d>

<d>51</d>

<d>87</d>

<d>25</d>

<d>48</d>

<d>117</d>

<d>35</d>

<d>57</d>

<d>62</d>

<d>46</d>

<d>56</d>

<d>46</d>

<d>50</d>

<d>54</d>

<d>60</d>

<d>37</d>

<d>55</d>

<d>66</d>

<d>51</d>

<d>32</d>

<d>43</d>

<d>57</d>

<d>46</d>

<d>45</d>

<d>34</d>

<d>41</d>

<d>110</d>

<d>40</d>

<d>44</d>

<d>49</d>

<d>51</d>

<d>59</d>

<d>39</d>

<d>77</d>

<d>30</d>

<d>31</d>

<d>31</d>

<d>34</d>

<d>56</d>

<d>33</d>

<d>46</d>

<d>47</d>

<d>29</d>

<d>37</d>

<d>38</d>

<d>34</d>

<d>54</d>

<d>34</d>

<d>83</d>

<d>36</d>

<d>48</d>

<d>33</d>

<d>40</d>

<d>36</d>

<d>44</d>

<d>44</d>

<d>54</d>

<d>42</d>

<d>62</d>

<d>32</d>

<d>62</d>

<d>63</d>

<d>44</d>

<d>47</d>

<d>45</d>

<d>68</d>

<d>40</d>

<d>43</d>

<d>34</d>

<d>33</d>

<d>55</d>

<d>58</d>

<d>46</d>

<d>42</d>

<d>50</d>

<d>52</d>

<d>57</d>

<d>70</d>

<d>40</d>

<d>41</d>

<d>37</d>

<d>67</d>

<d>70</d>

<d>38</d>

<d>66</d>

<d>60</d>

<d>42</d>

<d>41</d>

<d>92</d>

<d>26</d>

<d>53</d>

<d>83</d>

<d>59</d>

<d>58</d>

<d>45</d>

<d>78</d>

<d>46</d>

<d>68</d>

<d>34</d>

<d>28</d>

<d>44</d>

<d>50</d>

<d>36</d>

<d>58</d>

<d>91</d>

<d>42</d>

<d>58</d>

<d>36</d>

<d>66</d>

<d>46</d>

<d>30</d>

<d>64</d>

<d>83</d>

<d>36</d>

<d>28</d>

<d>40</d>

<d>36</d>

<d>49</d>

<d>67</d>

<d>34</d>

<d>42</d>

<d>46</d>

<d>56</d>

<d>43</d>

<d>44</d>

<d>38</d>

<d>59</d>

<d>65</d>

<d>68</d>

<d>36</d>

<d>63</d>

<d>37</d>

<d>36</d>

<d>34</d>

<d>58</d>

<d>54</d>

<d>36</d>

<d>51</d>

<d>31</d>

<d>52</d>

<d>54</d>

<d>47</d>

<d>42</d>

<d>87</d>

<d>55</d>

<d>46</d>

<d>36</d>

<d>84</d>

<d>60</d>

<d>33</d>

<d>65</d>

<d>26</d>

<d>62</d>

<d>90</d>

<d>46</d>

<d>92</d>

<d>38</d>

<d>40</d>

<d>36</d>

<d>48</d>

<d>57</d>

<d>52</d>

<d>40</d>

<d>28</d>

<d>40</d>

<d>67</d>

<d>48</d>

<d>81</d>

<d>69</d>

<d>58</d>

<d>50</d>

<d>64</d>

<d>118</d>

<d>46</d>

<d>54</d>

<d>38</d>

<d>52</d>

<d>79</d>

</Subcolumn>

</YColumn>

<YColumn Width="140" Decimals="0" Subcolumns="1">

<Title>Age in years</Title>

<Subcolumn>

<d>46</d>

<d>29</d>

<d>58</d>

<d>67</d>

<d>64</d>

<d>34</d>

<d>30</d>

<d>37</d>

<d>45</d>

<d>55</d>

<d>60</d>

<d>38</d>

<d>27</d>

<d>40</d>

<d>36</d>

<d>33</d>

<d>50</d>

<d>20</d>

<d>36</d>

<d>62</d>

<d>70</d>

<d>47</d>

<d>38</d>

<d>66</d>

<d>24</d>

<d>41</d>

<d>37</d>

<d>48</d>

<d>43</d>

<d>40</d>

<d>42</d>

<d>52</d>

<d>61</d>

<d>61</d>

<d>25</d>

<d>47</d>

<d>35</d>

<d>46</d>

<d>57</d>

<d>70</d>

<d>22</d>

<d>52</d>

<d>36</d>

<d>43</d>

<d>72</d>

<d>37</d>

<d>54</d>

<d>60</d>

<d>40</d>

<d>55</d>

<d>76</d>

<d>43</d>

<d>65</d>

<d>45</d>

<d>70</d>

<d>20</d>

<d>62</d>

<d>92</d>

<d>49</d>

<d>44</d>

<d>74</d>

<d>36</d>

<d>51</d>

<d>38</d>

<d>31</d>

<d>28</d>

<d>22</d>

<d>71</d>

<d>76</d>

<d>91</d>

<d>40</d>

<d>23</d>

<d>20</d>

<d>40</d>

<d>52</d>

<d>76</d>

<d>46</d>

<d>48</d>

<d>22</d>

<d>58</d>

<d>34</d>

<d>61</d>

<d>40</d>

<d>28</d>

<d>53</d>

<d>67</d>

<d>51</d>

<d>49</d>

<d>65</d>

<d>54</d>

<d>38</d>

<d>64</d>

<d>41</d>

<d>67</d>

<d>27</d>

<d>21</d>

<d>41</d>

<d>47</d>

<d>61</d>

<d>65</d>

<d>28</d>

<d>41</d>

<d>37</d>

<d>50</d>

<d>57</d>

<d>28</d>

<d>31</d>

<d>83</d>

<d>79</d>

<d>68</d>

<d>32</d>

<d>26</d>

<d>36</d>

<d>53</d>

<d>19</d>

<d>63</d>

<d>58</d>

<d>53</d>

<d>50</d>

<d>41</d>

<d>48</d>

<d>59</d>

<d>34</d>

<d>63</d>

<d>23</d>

<d>21</d>

<d>23</d>

<d>36</d>

<d>71</d>

<d>64</d>

<d>43</d>

<d>31</d>

<d>44</d>

<d>60</d>

<d>43</d>

<d>48</d>

<d>56</d>

<d>55</d>

<d>49</d>

<d>58</d>

<d>33</d>

<d>48</d>

<d>66</d>

<d>59</d>

<d>45</d>

<d>52</d>

<d>76</d>

<d>36</d>

<d>41</d>

<d>20</d>

<d>50</d>

<d>43</d>

<d>82</d>

<d>35</d>

<d>47</d>

<d>75</d>

<d>62</d>

<d>31</d>

<d>50</d>

<d>39</d>

<d>33</d>

<d>58</d>

<d>81</d>

<d>27</d>

<d>47</d>

<d>33</d>

<d>67</d>

<d>42</d>

<d>21</d>

<d>51</d>

<d>27</d>

<d>51</d>

<d>71</d>

<d>50</d>

<d>54</d>

<d>59</d>

<d>59</d>

<d>40</d>

<d>58</d>

<d>72</d>

<d>66</d>

<d>23</d>

<d>42</d>

<d>43</d>

<d>75</d>

<d>65</d>

<d>34</d>

<d>37</d>

<d>61</d>

<d>36</d>

<d>45</d>

<d>68</d>

<d>57</d>

<d>41</d>

<d>68</d>

<d>40</d>

<d>79</d>

<d>62</d>

<d>63</d>

<d>55</d>

<d>55</d>

<d>27</d>

<d>66</d>

<d>63</d>

<d>78</d>

<d>68</d>

<d>31</d>

<d>64</d>

<d>40</d>

<d>61</d>

<d>28</d>

<d>34</d>

<d>63</d>

<d>55</d>

<d>26</d>

<d>36</d>

<d>40</d>

<d>45</d>

<d>68</d>

<d>82</d>

<d>60</d>

<d>30</d>

<d>41</d>

<d>54</d>

<d>72</d>

<d>47</d>

<d>50</d>

<d>51</d>

<d>45</d>

<d>38</d>

<d>20</d>

<d>44</d>

<d>63</d>

<d>50</d>

<d>44</d>

<d>48</d>

<d>41</d>

<d>29</d>

<d>76</d>

<d>69</d>

<d>26</d>

<d>70</d>

<d>25</d>

<d>42</d>

<d>56</d>

<d>31</d>

<d>31</d>

<d>27</d>

<d>73</d>

<d>32</d>

<d>19</d>

<d>71</d>

<d>27</d>

<d>31</d>

<d>20</d>

<d>31</d>

<d>62</d>

<d>44</d>

<d>36</d>

<d>36</d>

<d>47</d>

<d>30</d>

<d>63</d>

<d>48</d>

<d>65</d>

<d>59</d>

<d>37</d>

<d>78</d>

<d>23</d>

<d>38</d>

<d>38</d>

<d>41</d>

<d>29</d>

<d>49</d>

<d>23</d>

<d>29</d>

<d>40</d>

<d>38</d>

<d>40</d>

<d>29</d>

<d>78</d>

<d>50</d>

<d>23</d>

<d>60</d>

<d>40</d>

<d>60</d>

<d>40</d>

<d>30</d>

<d>21</d>

<d>63</d>

<d>63</d>

<d>43</d>

<d>46</d>

<d>64</d>

<d>56</d>

<d>35</d>

<d>59</d>

<d>22</d>

<d>43</d>

<d>26</d>

<d>41</d>

<d>43</d>

<d>20</d>

<d>28</d>

<d>30</d>

<d>66</d>

<d>20</d>

<d>32</d>

<d>38</d>

<d>61</d>

<d>26</d>

<d>74</d>

<d>72</d>

<d>21</d>

<d>36</d>

<d>42</d>

<d>66</d>

<d>34</d>

<d>43</d>

<d>57</d>

<d>45</d>

<d>44</d>

<d>27</d>

<d>63</d>

<d>65</d>

<d>30</d>

<d>28</d>

<d>41</d>

<d>31</d>

<d>33</d>

<d>66</d>

<d>28</d>

<d>25</d>

<d>26</d>

<d>40</d>

<d>38</d>

<d>30</d>

<d>52</d>

<d>22</d>

<d>51</d>

<d>45</d>

<d>53</d>

<d>21</d>

<d>53</d>

<d>37</d>

<d>34</d>

<d>30</d>

<d>74</d>

<d>36</d>

<d>45</d>

<d>35</d>

<d>50</d>

<d>27</d>

<d>52</d>

<d>42</d>

<d>39</d>

<d>73</d>

<d>28</d>

<d>53</d>

<d>49</d>

<d>55</d>

<d>37</d>

<d>60</d>

<d>56</d>

<d>84</d>

<d>20</d>

<d>80</d>

<d>60</d>

<d>80</d>

<d>29</d>

<d>43</d>

<d>63</d>

<d>37</d>

<d>20</d>

<d>44</d>

<d>54</d>

<d>58</d>

<d>35</d>

<d>52</d>

<d>60</d>

<d>43</d>

<d>59</d>

<d>33</d>

<d>37</d>

<d>40</d>

<d>38</d>

<d>32</d>

<d>60</d>

<d>30</d>

<d>42</d>

<d>52</d>

<d>59</d>

<d>78</d>

<d>51</d>

<d>25</d>

<d>37</d>

<d>54</d>

<d>89</d>

<d>53</d>

<d>51</d>

<d>29</d>

<d>41</d>

<d>68</d>

</Subcolumn>

</YColumn>

<YColumn Width="140" Decimals="0" Subcolumns="1">

<Title>Male?</Title>

<Subcolumn>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>1</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>1</d>

<d>0</d>

<d>0</d>

<d>0</d>

<d>0</d>

</Subcolumn>

</YColumn>

<YColumn Width="159" Decimals="0" Subcolumns="1">

<Title>Height in inches</Title>

<Subcolumn>

<d>62</d>

<d>64</d>

<d>61</d>

<d>67</d>

<d>68</d>

<d>71</d>

<d>69</d>

<d>59</d>

<d>69</d>

<d>63</d>

<d>65</d>

<d>58</d>

<d>60</d>

<d>59</d>

<d>69</d>

<d>65</d>

<d>65</d>

<d>67</d>

<d>64</d>

<d>65</d>

<d>67</d>

<d>67</d>

<d>69</d>

<d>62</d>

<d>61</d>

<d>72</d>

<d>68</d>

<d>68</d>

<d>62</d>

<d>70</d>

<d>65</d>

<d>68</d>

<d>74</d>

<d>69</d>

<d>66</d>

<d>66</d>

<d>66</d>

<d>72</d>

<d>71</d>

<d>69</d>

<d>66</d>

<d>62</d>

<d>67</d>

<d>75</d>

<d>59</d>

<d>64</d>

<d>67</d>

<d>67</d>

<d>65</d>

<d>66</d>

<d>65</d>

<d>64</d>

<d>61</d>

<d>64</d>

<d>60</d>

<d>68</d>

<d>68</d>

<d>62</d>

<d>62</d>

<d>66</d>

<d>63</d>

<d>70</d>

<d>70</d>

<d/>

<d>70</d>

<d>64</d>

<d>62</d>

<d>63</d>

<d>61</d>

<d>61</d>

<d>71</d>

<d>69</d>

<d>64</d>

<d>68</d>

<d>75</d>

<d>60</d>

<d>62</d>

<d>70</d>

<d>71</d>

<d>63</d>

<d>73</d>

<d>67</d>

<d>73</d>

<d>64</d>

<d>61</d>

<d>67</d>

<d/>

<d>62</d>

<d>62</d>

<d>66</d>

<d>68</d>

<d>63</d>

<d>61</d>

<d>68</d>

<d>67</d>

<d>63</d>

<d>59</d>

<d>63</d>

<d>65</d>

<d>67</d>

<d>63</d>

<d>69</d>

<d>61</d>

<d>71</d>

<d>61</d>

<d>68</d>

<d>67</d>

<d>59</d>

<d>70</d>

<d>70</d>

<d>70</d>

<d>72</d>

<d>69</d>

<d>64</d>

<d>61</d>

<d>73</d>

<d>63</d>

<d>63</d>

<d>70</d>

<d>71</d>

<d>70</d>

<d>67</d>

<d>72</d>

<d>63</d>

<d>76</d>

<d>65</d>

<d>65</d>

<d>63</d>

<d>63</d>

<d>71</d>

<d>64</d>

<d>67</d>

<d>64</d>

<d>65</d>

<d>64</d>

<d>63</d>

<d>74</d>

<d>67</d>

<d>71</d>

<d>67</d>

<d>63</d>

<d>68</d>

<d>66</d>

<d>66</d>

<d>67</d>

<d>71</d>

<d>64</d>

<d>70</d>

<d>62</d>

<d>72</d>

<d>71</d>

<d>62</d>

<d>66</d>

<d>69</d>

<d>65</d>

<d>68</d>

<d>76</d>

<d>62</d>

<d>70</d>

<d>66</d>

<d>69</d>

<d>69</d>

<d>64</d>

<d>65</d>

<d>64</d>

<d>66</d>

<d>68</d>

<d>75</d>

<d>63</d>

<d>65</d>

<d>63</d>

<d>67</d>

<d>69</d>

<d>69</d>

<d>65</d>

<d>73</d>

<d>66</d>

<d>59</d>

<d>66</d>

<d>65</d>

<d>66</d>

<d>72</d>

<d>65</d>

<d>71</d>

<d>69</d>

<d>63</d>

<d>71</d>

<d>71</d>

<d>63</d>

<d>64</d>

<d>67</d>

<d>61</d>

<d>66</d>

<d>62</d>

<d>67</d>

<d/>

<d>65</d>

<d>66</d>

<d>61</d>

<d>70</d>

<d>63</d>

<d>67</d>

<d>68</d>

<d>64</d>

<d>65</d>

<d>55</d>

<d>66</d>

<d>62</d>

<d>68</d>

<d>63</d>

<d>69</d>

<d>63</d>

<d>65</d>

<d>75</d>

<d>73</d>

<d>64</d>

<d>62</d>

<d>69</d>

<d>63</d>

<d>63</d>

<d>62</d>

<d>65</d>

<d>67</d>

<d>65</d>

<d>61</d>

<d>68</d>

<d>61</d>

<d>66</d>

<d>69</d>

<d>63</d>

<d>70</d>

<d/>

<d>70</d>

<d>67</d>

<d>67</d>

<d>68</d>

<d>63</d>

<d>68</d>

<d>66</d>

<d>59</d>

<d>72</d>

<d>66</d>

<d>71</d>

<d>62</d>

<d>68</d>

<d>67</d>

<d>65</d>

<d>65</d>

<d>64</d>

<d>63</d>

<d>60</d>

<d>63</d>

<d>63</d>

<d>71</d>

<d>62</d>

<d>66</d>

<d>63</d>

<d>71</d>

<d>66</d>

<d>69</d>

<d>73</d>

<d>72</d>

<d>69</d>

<d>63</d>

<d>69</d>

<d>63</d>

<d>64</d>

<d>66</d>

<d>71</d>

<d>69</d>

<d>69</d>

<d>67</d>

<d>65</d>

<d>63</d>

<d>65</d>

<d>62</d>

<d>72</d>

<d>60</d>

<d>63</d>

<d>68</d>

<d>63</d>

<d>71</d>

<d>61</d>

<d>70</d>

<d>52</d>

<d>61</d>

<d>62</d>

<d>62</d>

<d>69</d>

<d>64</d>

<d>70</d>

<d>67</d>

<d>63</d>

<d>68</d>

<d>69</d>

<d>74</d>

<d>66</d>

<d>63</d>

<d>64</d>

<d>62</d>

<d>65</d>

<d>65</d>

<d>64</d>

<d>68</d>

<d>63</d>

<d>67</d>

<d>70</d>

<d>65</d>

<d>64</d>

<d>74</d>

<d>60</d>

<d>62</d>

<d>70</d>

<d>71</d>

<d>66</d>

<d>61</d>

<d>64</d>

<d/>

<d>69</d>

<d>73</d>

<d>66</d>

<d>62</d>

<d>72</d>

<d>69</d>

<d>70</d>

<d>72</d>

<d>66</d>

<d>71</d>

<d>62</d>

<d>69</d>

<d>74</d>

<d>67</d>

<d>63</d>

<d>70</d>

<d>64</d>

<d>68</d>

<d>62</d>

<d>64</d>

<d>64</d>

<d>65</d>

<d>69</d>

<d>63</d>

<d>61</d>

<d>64</d>

<d>67</d>

<d>60</d>

<d>66</d>

<d>69</d>

<d>71</d>

<d>67</d>

<d>73</d>

<d>64</d>

<d>69</d>

<d>69</d>

<d>67</d>

<d>60</d>

<d>65</d>

<d>67</d>

<d>69</d>

<d>67</d>

<d>66</d>

<d>64</d>

<d>59</d>

<d>65</d>

<d>60</d>

<d>67</d>

<d>71</d>

<d>69</d>

<d>62</d>

<d>64</d>

<d>66</d>

<d>58</d>

<d>59</d>

<d>58</d>

<d>68</d>

<d>69</d>

<d>70</d>

<d>72</d>

<d>70</d>

<d>63</d>

<d>56</d>

<d>68</d>

<d>62</d>

<d>70</d>

<d>72</d>

<d>67</d>

<d>62</d>

<d>68</d>

<d>72</d>

<d>66</d>

<d>62</d>

<d>62</d>

<d>66</d>

<d>65</d>

<d>60</d>

<d>66</d>

<d>66</d>

<d>61</d>

<d>69</d>

<d>63</d>

<d>69</d>

<d>63</d>

<d>64</d>

</Subcolumn>

</YColumn>

<YColumn Width="173" Decimals="0" Subcolumns="1">

<Title>Weight in pounds</Title>

<Subcolumn>

<d>121</d>

<d>218</d>

<d>256</d>

<d>119</d>

<d>183</d>

<d>190</d>

<d>191</d>

<d>170</d>

<d>166</d>

<d>202</d>

<d>156</d>

<d>195</d>

<d>170</d>

<d>165</d>

<d>183</d>

<d>157</d>

<d>183</d>

<d>159</d>

<d>126</d>

<d>196</d>

<d>178</d>

<d>230</d>

<d>288</d>

<d>185</d>

<d>113</d>

<d>118</d>

<d>252</d>

<d>100</d>

<d>145</d>

<d>189</d>

<d>174</d>

<d>139</d>

<d>191</d>

<d>174</d>

<d>118</d>

<d>186</d>

<d>159</d>

<d>205</d>

<d>145</d>

<d>214</d>

<d>160</d>

<d>170</d>

<d>192</d>

<d>253</d>

<d>137</d>

<d>233</d>

<d>165</d>

<d>196</d>

<d>180</d>

<d>219</d>

<d>154</d>

<d>181</d>

<d>187</d>

<d>167</d>

<d>220</d>

<d>274</d>

<d>180</d>

<d>217</d>

<d>189</d>

<d>191</d>

<d>183</d>

<d>161</d>

<d>235</d>

<d>125</d>

<d>165</d>

<d>126</d>

<d>137</d>

<d>165</d>

<d>102</d>

<d>127</d>

<d>214</d>

<d>245</d>

<d>161</d>

<d>264</d>

<d>142</d>

<d>143</d>

<d>183</d>

<d>173</d>

<d>223</d>

<d>154</d>

<d>219</d>

<d>169</d>

<d>200</d>

<d>200</d>

<d>145</d>

<d>178</d>

<d>215</d>

<d>205</d>

<d>151</d>

<d>170</d>

<d>169</d>

<d>159</d>

<d>110</d>

<d>198</d>

<d>185</d>

<d>142</d>

<d>139</d>

<d>156</d>

<d>220</d>

<d>197</d>

<d>200</d>

<d>154</d>

<d>203</d>

<d>180</d>

<d>150</d>

<d>204</d>

<d>200</d>

<d>125</d>

<d>165</d>

<d>170</d>

<d>212</d>

<d>227</d>

<d>150</d>

<d>174</d>

<d>119</d>

<d>175</d>

<d>230</d>

<d>158</d>

<d>263</d>

<d>156</d>

<d>120</d>

<d>172</d>

<d>170</d>

<d>158</d>

<d>164</d>

<d>169</d>

<d>235</d>

<d>125</d>

<d>244</d>

<d>225</d>

<d>140</d>

<d>227</d>

<d>160</d>

<d>167</d>

<d>325</d>

<d>121</d>

<d>151</d>

<d>223</d>

<d>266</d>

<d>177</d>

<d>170</d>

<d>146</d>

<d>121</d>

<d>170</d>

<d>151</d>

<d>159</d>

<d>105</d>

<d>277</d>

<d>160</d>

<d>145</d>

<d>320</d>

<d>163</d>

<d>163</d>

<d>169</d>

<d>232</d>

<d>210</d>

<d>160</d>

<d>145</d>

<d>215</d>

<d>255</d>

<d>308</d>

<d/>

<d>158</d>

<d>210</d>

<d>123</d>

<d>118</d>

<d>167</d>

<d>186</d>

<d>158</d>

<d>145</d>

<d>119</d>

<d>282</d>

<d>171</d>

<d>172</d>

<d>138</d>

<d>187</d>

<d>189</d>

<d>204</d>

<d>215</d>

<d>167</d>

<d>189</d>

<d>180</d>

<d>165</d>

<d>179</d>

<d>204</d>

<d>233</d>

<d>210</d>

<d>195</d>

<d>199</d>

<d>185</d>

<d>147</d>

<d>119</d>

<d>171</d>

<d>184</d>

<d>158</d>

<d>130</d>

<d>134</d>

<d>251</d>

<d>200</d>

<d>140</d>

<d>114</d>

<d>209</d>

<d>210</d>

<d>179</d>

<d>109</d>

<d>130</d>

<d>145</d>

<d>167</d>

<d>179</d>

<d>144</d>

<d>130</d>

<d>164</d>

<d>201</d>

<d>186</d>

<d>174</d>

<d>136</d>

<d>105</d>

<d>130</d>

<d>124</d>

<d>170</d>

<d>134</d>

<d>165</d>

<d>191</d>

<d>175</d>

<d>180</d>

<d>142</d>

<d>147</d>

<d>110</d>

<d>204</d>

<d>181</d>

<d>187</d>

<d>190</d>

<d>181</d>

<d>140</d>

<d>201</d>

<d>196</d>

<d>153</d>

<d>170</d>

<d>188</d>

<d>179</d>

<d>259</d>

<d>200</d>

<d>162</d>

<d>141</d>

<d>183</d>

<d>160</d>

<d>120</d>

<d>145</d>

<d>174</d>

<d>252</d>

<d>135</d>

<d>155</d>

<d>179</d>

<d>211</d>

<d>115</d>

<d>190</d>

<d>290</d>

<d>168</d>

<d>255</d>

<d>205</d>

<d>260</d>

<d>250</d>

<d>166</d>

<d>170</d>

<d>182</d>

<d>176</d>

<d>145</d>

<d>172</d>

<d>277</d>

<d>167</d>

<d>205</d>

<d>183</d>

<d>123</d>

<d>128</d>

<d>183</d>

<d>99</d>

<d>270</d>

<d>138</d>

<d>285</d>

<d>180</d>

<d>160</d>

<d>170</d>

<d>185</d>

<d>163</d>

<d>187</d>

<d>128</d>

<d>153</d>

<d>125</d>

<d>155</d>

<d>223</d>

<d>161</d>

<d>216</d>

<d>179</d>

<d>227</d>

<d>159</d>

<d>170</d>

<d>138</d>

<d>114</d>

<d>239</d>

<d>174</d>

<d>188</d>

<d>198</d>

<d>114</d>

<d>225</d>

<d>143</d>

<d>146</d>

<d>141</d>

<d>151</d>

<d>248</d>

<d>152</d>

<d>130</d>

<d>165</d>

<d>180</d>

<d>163</d>

<d>179</d>

<d>156</d>

<d>130</d>

<d>160</d>

<d>210</d>

<d>164</d>

<d>115</d>

<d>159</d>

<d>141</d>

<d>169</d>

<d>181</d>

<d>180</d>

<d>209</d>

<d>210</d>

<d>237</d>

<d>163</d>

<d>185</d>

<d>180</d>

<d>245</d>

<d>150</d>

<d>146</d>

<d>145</d>

<d>142</d>

<d>198</d>

<d>148</d>

<d>200</d>

<d>190</d>

<d>182</d>

<d>220</d>

<d>179</d>

<d>212</d>

<d>165</d>

<d>257</d>

<d>184</d>

<d>183</d>

<d>218</d>

<d>179</d>

<d>228</d>

<d>289</d>

<d>153</d>

<d>235</d>

<d>144</d>

<d>183</d>

<d>154</d>

<d>216</d>

<d>181</d>

<d>202</d>

<d>160</d>

<d>123</d>

<d>197</d>

<d>192</d>

<d>187</d>

<d>212</d>

<d>186</d>

<d>162</d>

<d>120</d>

<d>152</d>

<d>210</d>

<d>148</d>

<d>170</d>

<d>157</d>

<d>129</d>

<d>211</d>

<d>189</d>

<d>120</d>

<d>121</d>

<d>120</d>

<d>169</d>

<d>186</d>

<d>262</d>

<d>222</d>

<d>222</d>

<d>179</d>

<d>224</d>

<d>165</d>

<d>185</d>

<d>147</d>

<d>177</d>

<d>145</d>

<d>146</d>

<d>154</d>

<d>136</d>

<d>168</d>

<d>115</d>

<d>173</d>

<d>154</d>

<d>167</d>

<d>197</d>

<d>220</d>

</Subcolumn>

</YColumn>

<YColumn Width="152" Decimals="0" Subcolumns="1">

<Title>Waist in inches</Title>

<Subcolumn>

<d>29</d>

<d>46</d>

<d>49</d>

<d>33</d>

<d>44</d>

<d>36</d>

<d>46</d>

<d>34</d>

<d>34</d>

<d>45</d>

<d>39</d>

<d>42</d>

<d>35</d>

<d>37</d>

<d>36</d>

<d>37</d>

<d>37</d>

<d>31</d>

<d>30</d>

<d>46</d>

<d>42</d>

<d>45</d>

<d>48</d>

<d>48</d>

<d>33</d>

<d>28</d>

<d>43</d>

<d>27</d>

<d>31</d>

<d>37</d>

<d>37</d>

<d>29</d>

<d>39</d>

<d>49</d>

<d>32</d>

<d>39</d>

<d>31</d>

<d>46</d>

<d>31</d>

<d>45</d>

<d>35</d>

<d>40</d>

<d>40</d>

<d>43</d>

<d>40</d>

<d>49</d>

<d>42</d>

<d>42</d>

<d>40</d>

<d>43</d>

<d>37</d>

<d>36</d>

<d>43</d>

<d>39</d>

<d>51</d>

<d>49</d>

<d>38</d>

<d>51</d>

<d>43</d>

<d>40</d>

<d>42</d>

<d>34</d>

<d>43</d>

<d>31</d>

<d>35</d>

<d>28</d>

<d>28</d>

<d>34</d>

<d>31</d>

<d>35</d>

<d>41</d>

<d>44</d>

<d>37</d>

<d>43</d>

<d>31</d>

<d>35</d>

<d>37</d>

<d>36</d>

<d>46</d>

<d>38</d>

<d>41</d>

<d>40</d>

<d>38</d>

<d>38</d>

<d>37</d>

<d>37</d>

<d>42</d>

<d>40</d>

<d>37</d>

<d>36</d>

<d>34</d>

<d>33</d>

<d>29</d>

<d>36</d>

<d>35</d>

<d>28</d>

<d>29</d>

<d>35</d>

<d>40</d>

<d>42</d>

<d>42</d>

<d>34</d>

<d>42</d>

<d>36</d>

<d>36</d>

<d>40</d>

<d>41</d>

<d>35</d>

<d>39</d>

<d>37</d>

<d>39</d>

<d>41</d>

<d>31</d>

<d>34</d>

<d>32</d>

<d>34</d>

<d>39</d>

<d>33</d>

<d>51</d>

<d>31</d>

<d>32</d>

<d>38</d>

<d>31</d>

<d>34</d>

<d>32</d>

<d>39</d>

<d>44</d>

<d>33</d>

<d>48</d>

<d>44</d>

<d>37</d>

<d>47</d>

<d>40</d>

<d>38</d>

<d>53</d>

<d>32</d>

<d>34</d>

<d>43</d>

<d>49</d>

<d>38</d>

<d>33</d>

<d>32</d>

<d>31</d>

<d>37</d>

<d>33</d>

<d>33</d>

<d>29</d>

<d>51</d>

<d>39</d>

<d>29</d>

<d>56</d>

<d>40</d>

<d>37</d>

<d>39</d>

<d>46</d>

<d>44</d>

<d>35</d>

<d>36</d>

<d>40</d>

<d>46</d>

<d>52</d>

<d>30</d>

<d>36</d>

<d>39</d>

<d>30</d>

<d>29</d>

<d>36</d>

<d>37</d>

<d>39</d>

<d>38</d>

<d>28</d>

<d>52</d>

<d>38</d>

<d>37</d>

<d>33</d>

<d>38</d>

<d>38</d>

<d>40</d>

<d>46</d>

<d>45</d>

<d>45</d>

<d>34</d>

<d>33</d>

<d>37</d>

<d>44</d>

<d>40</d>

<d>37</d>

<d>36</d>

<d>41</d>

<d>39</d>

<d>32</d>

<d>29</d>

<d>37</d>

<d>39</d>

<d>37</d>

<d>37</d>

<d>34</d>

<d>50</d>

<d>44</d>

<d>32</d>

<d>30</d>

<d>34</d>

<d>45</d>

<d>47</d>

<d>33</d>

<d>29</d>

<d>33</d>

<d>44</d>

<d>37</d>

<d>40</d>

<d>29</d>

<d>34</d>

<d>45</d>

<d>38</d>

<d>30</d>

<d>32</d>

<d>26</d>

<d>33</d>

<d>32</d>

<d>41</d>

<d>31</d>

<d>33</d>

<d>38</d>

<d>37</d>

<d>39</d>

<d>35</d>

<d>34</d>

<d>28</d>

<d>40</d>

<d>38</d>

<d>37</d>

<d>38</d>

<d>37</d>

<d>35</d>

<d>46</d>

<d>38</d>

<d>32</d>

<d>38</d>

<d>40</d>

<d>45</d>

<d>48</d>

<d>41</d>

<d>35</d>

<d>33</d>

<d>36</d>

<d>30</d>

<d>29</d>

<d>32</d>

<d>38</d>

<d>45</d>

<d>33</d>

<d>33</d>

<d>40</d>

<d>40</d>

<d>31</d>

<d>41</d>

<d>55</d>

<d>36</d>

<d>47</d>

<d>37</d>

<d>42</d>

<d>43</d>

<d>39</d>

<d>35</d>

<d>42</d>

<d>34</d>

<d>34</d>

<d>40</d>

<d>50</d>

<d>36</d>

<d>39</d>

<d>38</d>

<d>26</d>

<d>31</d>

<d>43</d>

<d>30</d>

<d>45</d>

<d>31</d>

<d>50</d>

<d>38</d>

<d>36</d>

<d>34</d>

<d>43</d>

<d>36</d>

<d>38</d>

<d>33</d>

<d>36</d>

<d>31</d>

<d>31</d>

<d>45</d>

<d>37</d>

<d>41</d>

<d>38</d>

<d>44</d>

<d>38</d>

<d>32</d>

<d>32</d>

<d>28</d>

<d>48</d>

<d>38</d>

<d>35</d>

<d>40</d>

<d>31</d>

<d>41</d>

<d>33</d>

<d>36</d>

<d>32</d>

<d>33</d>

<d>49</d>

<d>33</d>

<d>32</d>

<d>41</d>

<d>39</d>

<d>34</d>

<d>37</d>

<d>36</d>

<d>31</d>

<d>40</d>

<d>44</d>

<d>31</d>

<d>30</d>

<d>36</d>

<d>33</d>

<d>35</d>

<d>43</d>

<d>35</d>

<d>42</d>

<d>37</d>

<d>53</d>

<d>35</d>

<d>40</d>

<d>39</d>

<d>47</d>

<d>33</d>

<d>37</d>

<d>34</d>

<d/>

<d>42</d>

<d>35</d>

<d>43</d>

<d>39</d>

<d>38</d>

<d>40</d>

<d>39</d>

<d>48</d>

<d>34</d>

<d>47</d>

<d>39</d>

<d>41</d>

<d>41</d>

<d>32</d>

<d>42</d>

<d>48</d>

<d>36</d>

<d>47</d>

<d>33</d>

<d>43</d>

<d>35</d>

<d>43</d>

<d>41</d>

<d>43</d>

<d>37</d>

<d>36</d>

<d>41</d>

<d>41</d>

<d>40</d>

<d>47</d>

<d>40</d>

<d>44</d>

<d>33</d>

<d>37</d>

<d>44</d>

<d>32</d>

<d>34</d>

<d>33</d>

<d>30</d>

<d>38</d>

<d>36</d>

<d>30</d>

<d>32</d>

<d>32</d>

<d>36</d>

<d>42</d>

<d>42</d>

<d>40</d>

<d>41</d>

<d>37</d>

<d>48</d>

<d>31</d>

<d>39</d>

<d>34</d>

<d>39</d>

<d>38</d>

<d/>

<d>40</d>

<d>33</d>

<d>38</d>

<d>31</d>

<d>35</d>

<d>32</d>

<d>33</d>

<d>41</d>

<d>49</d>

</Subcolumn>

</YColumn>

<YColumn Width="140" Decimals="0" Subcolumns="1">

<Title>Hip in inches</Title>

<Subcolumn>

<d>38</d>

<d>48</d>

<d>57</d>

<d>38</d>

<d>41</d>

<d>42</d>

<d>49</d>

<d>39</d>

<d>40</d>

<d>50</d>

<d>45</d>

<d>50</d>

<d>41</d>

<d>43</d>

<d>40</d>

<d>41</d>

<d>43</d>

<d>39</d>

<d>34</d>

<d>51</d>

<d>41</d>

<d>46</d>

<d>55</d>

<d>44</d>

<d>38</d>

<d>36</d>

<d>47</d>

<d>33</d>

<d>38</d>

<d>39</d>

<d>40</d>

<d>35</d>

<d>41</d>

<d>43</d>

<d>34</d>

<d>44</d>

<d>35</d>

<d>49</d>

<d>36</d>

<d>48</d>

<d>40</d>

<d>43</d>

<d>42</d>

<d>49</d>

<d>40</d>

<d>57</d>

<d>42</d>

<d>43</d>

<d>44</d>

<d>52</d>

<d>41</d>

<d>46</d>

<d>47</d>

<d>44</d>

<d>54</d>

<d>58</d>

<d>41</d>

<d>51</d>

<d>47</d>

<d>45</d>

<d>48</d>

<d>40</d>

<d>48</d>

<d>35</d>

<d>39</d>

<d>32</d>

<d>35</d>

<d>42</d>

<d>33</d>

<d>38</d>

<d>39</d>

<d>47</d>

<d>40</d>

<d>54</d>

<d>35</d>

<d>40</d>

<d>45</d>

<d>40</d>

<d>50</d>

<d>41</d>

<d>42</d>

<d>44</d>

<d>41</d>

<d>45</d>

<d>40</d>

<d>41</d>

<d>53</d>

<d>49</d>

<d>42</d>

<d>41</d>

<d>40</d>

<d>41</d>

<d>30</d>

<d>43</d>

<d>44</d>

<d>39</d>

<d>39</d>

<d>39</d>

<d>50</d>

<d>43</d>

<d>46</d>

<d>39</d>

<d>51</d>

<d>40</d>

<d>39</d>

<d>41</d>

<d>42</d>

<d>40</d>

<d>41</d>

<d>42</d>

<d>44</d>

<d>44</d>

<d>38</d>

<d>43</d>

<d>38</d>

<d>39</d>

<d>48</d>

<d>40</d>

<d>64</d>

<d>40</d>

<d>35</d>

<d>43</d>

<d>39</d>

<d>42</d>

<d>40</d>

<d>43</d>

<d>50</d>

<d>36</d>

<d>51</d>

<d>47</d>

<d>40</d>

<d>53</d>

<d>43</d>

<d>44</d>

<d>62</d>

<d>38</d>

<d>38</d>

<d>48</d>

<d>45</d>

<d>43</d>

<d>46</d>

<d>41</d>

<d>33</d>

<d>40</d>

<d>38</d>

<d>39</d>

<d>33</d>

<d>49</d>

<d>41</d>

<d>36</d>

<d>49</d>

<d>45</d>

<d>43</d>

<d>41</d>

<d>54</d>

<d>45</d>

<d>39</d>

<d>42</d>

<d>45</d>

<d>54</d>

<d>58</d>

<d>37</d>

<d>43</d>

<d>47</d>

<d>36</d>

<d>35</d>

<d>39</d>

<d>46</d>

<d>43</d>

<d>42</d>

<d>37</d>

<d>59</d>

<d>40</d>

<d>41</d>

<d>39</d>

<d>37</d>

<d>45</d>

<d>47</d>

<d>44</d>

<d>46</d>

<d>46</d>

<d>41</d>

<d>45</d>

<d>44</d>

<d>42</d>

<d>53</d>

<d>43</d>

<d>43</d>

<d>47</d>

<d>45</d>

<d>38</d>

<d>37</d>

<d>40</d>

<d>44</d>

<d>43</d>

<d>38</d>

<d>38</d>

<d>47</d>

<d>48</d>

<d>33</d>

<d>37</d>

<d>43</d>

<d>48</d>

<d>45</d>

<d>34</d>

<d>33</d>

<d>38</d>

<d>47</d>

<d>43</d>

<d>42</d>

<d>35</d>

<d>43</d>

<d>46</d>

<d>38</d>

<d>35</d>

<d>37</d>

<d>33</d>

<d>35</d>

<d>38</d>

<d>46</d>

<d>40</d>

<d>42</d>

<d>41</d>

<d>43</d>

<d>47</d>

<d>39</d>

<d>41</d>

<d>37</d>

<d>43</d>

<d>46</d>

<d>41</d>

<d>44</d>

<d>42</d>

<d>37</d>

<d>49</d>

<d>42</d>

<d>40</d>

<d>39</d>

<d>41</d>

<d>48</d>

<d>49</d>

<d>47</d>

<d>39</d>

<d>40</d>

<d>39</d>

<d>44</d>

<d>40</d>

<d>35</d>

<d>44</d>

<d>58</d>

<d>40</d>

<d>41</d>

<d>42</d>

<d>45</d>

<d>37</d>

<d>47</d>

<d>62</d>

<d>39</d>

<d>52</d>

<d>41</d>

<d>47</d>

<d>51</d>

<d>38</d>

<d>42</d>

<d>39</d>

<d>44</d>

<d>42</d>

<d>40</d>

<d>49</d>

<d>47</d>

<d>41</d>

<d>40</d>

<d>37</d>

<d>36</d>

<d>45</d>

<d>36</d>

<d>49</d>

<d>39</d>

<d>60</d>

<d>42</d>

<d>45</d>

<d>39</d>

<d>47</d>

<d>40</d>

<d>49</d>

<d>39</d>

<d>44</d>

<d>39</d>

<d>39</d>

<d>54</d>

<d>40</d>

<d>44</d>

<d>46</d>

<d>47</d>

<d>39</d>

<d>38</d>

<d>38</d>

<d>35</d>

<d>53</d>

<d>46</d>

<d>46</d>

<d>49</d>

<d>39</d>

<d>52</d>

<d>40</d>

<d>48</d>

<d>39</d>

<d>40</d>

<d>58</d>

<d>38</d>

<d>40</d>

<d>46</d>

<d>40</d>

<d>39</d>

<d>42</d>

<d>42</d>

<d>38</d>

<d>47</d>

<d>47</d>

<d>37</d>

<d>34</d>

<d>43</d>

<d>38</d>

<d>39</d>

<d>49</d>

<d>41</d>

<d>46</d>

<d>42</d>

<d>56</d>

<d>38</d>

<d>41</d>

<d>43</d>

<d>58</d>

<d>39</d>

<d>43</d>

<d>37</d>

<d/>

<d>49</d>

<d>38</d>

<d>46</d>

<d>44</d>

<d>46</d>

<d>52</d>

<d>47</d>

<d>51</d>

<d>46</d>

<d>55</d>

<d>41</d>

<d>45</d>

<d>45</d>

<d>37</d>

<d>54</d>

<d>51</d>

<d>40</d>

<d>52</d>

<d>42</d>

<d>46</d>

<d>38</d>

<d>45</d>

<d>42</d>

<d>47</d>

<d>45</d>

<d>41</d>

<d>50</d>

<d>48</d>

<d>45</d>

<d>48</d>

<d>42</d>

<d>41</d>

<d>38</d>

<d>41</d>

<d>53</d>

<d>42</d>

<d>46</d>

<d>37</d>

<d>37</d>

<d>43</d>

<d>44</d>

<d>33</d>

<d>34</d>

<d>41</d>

<d>38</d>

<d>46</d>

<d>48</d>

<d>44</d>

<d>51</d>

<d>47</d>

<d>50</d>

<d>38</d>

<d>41</d>

<d>42</d>

<d>45</d>

<d>39</d>

<d/>

<d>42</d>

<d>39</d>

<d>42</d>

<d>41</d>

<d>39</d>

<d>43</d>

<d>40</d>

<d>48</d>

<d>58</d>

</Subcolumn>

</YColumn>

</Table>

</GraphPadPrismFile>

	<xsl:template match="ps:Info">

		<a><xsl:attribute name="name"><xsl:value-of select="@ID"/></xsl:attribute></a>

		<table border="0">

			<tr><td><xsl:apply-templates select="ps:Title"/> </td><td width="20"></td><td><b>Notes</b></td></tr>

			<tr><td valign="top">

					<table BORDER="1" CELLSPACING="0">

						<tr><th>Constant</th><th>Name</th></tr>

						<xsl:for-each select="ps:Constant">

							<tr>

								<td><xsl:apply-templates select="ps:Name"/> </td>

								<td><xsl:apply-templates select="ps:Value"/> </td>

							</tr>

						</xsl:for-each>

					</table>

				</td>

				<td width="20">

				</td>

				<td valign="top">

					<xsl:apply-templates select="ps:Notes"/>

				</td></tr>

		</table>

	</xsl:template>

	<xsl:template match="ps:GraphPadPrismFile">

		<HTML>

			<BODY>

				<xsl:variable name="Chars" select="'ABCDEFGHIJKLMNOPQRSTUVWXYZ'"/>

				<font color="#999999">

					<p align="center">This file can be opened by <a href="http://www.graphpad.com">GraphPad</a> Prism (version 

	<xsl:value-of select="@PrismXMLVersion"/> or later). </p></font><hr/>

         This file contains <xsl:value-of select="count(ps:Table|ps:HugeTable)"/> data tables and 

	<xsl:value-of select="count(ps:Info)+count(ps:Table|ps:HugeTable/ps:Info)"/> info tables:



	<dir>

					<xsl:for-each select="ps:Info">

						<li>

							<a>

								<xsl:attribute name="href">#<xsl:value-of select="@ID"/></xsl:attribute>

								<xsl:apply-templates select="ps:Title"/>

							</a>

						</li>

					</xsl:for-each>

					<xsl:for-each select="ps:Table|ps:HugeTable">

						<li>

							<a>

								<xsl:attribute name="href">#<xsl:value-of select="@ID"/></xsl:attribute>

								<xsl:apply-templates select="ps:Title"/>

							</a>

							<dir>

								<xsl:for-each select="ps:Info">

									<li>

										<a>

											<xsl:attribute name="href">#<xsl:value-of select="@ID"/></xsl:attribute>

											<xsl:apply-templates select="ps:Title"/>

										</a>

									</li>

								</xsl:for-each>

							</dir>

						</li>

					</xsl:for-each>

				</dir>

				<hr/>

				<xsl:apply-templates select="ps:Info"/>

				<br/>

				<hr/>

				<br/>

				<xsl:for-each select="ps:Table|ps:HugeTable">

					<xsl:apply-templates select="ps:Title"/>

					<a><xsl:attribute name="name"><xsl:value-of select="@ID"/></xsl:attribute></a>

					<xsl:variable name="width" select="100 div count (*/ps:Subcolumn)"/>

					<xsl:variable name="xFormat" select="./@XFormat"/>

					<xsl:variable name="yFormat" select="./@YFormat"/>

					<xsl:variable name="nRepl" select="./@replicates"/>

					<xsl:variable name="isXAdvancedColumn" select="boolean(($xFormat='date' or $xFormat='time' or $xFormat='startenddate') and count(ps:XAdvancedColumn) &gt; 0)"/>

					<xsl:variable name="isXAdvancedSubcol" select="boolean(($xFormat='startenddate') or count(ps:XAdvancedColumn/ps:Subcolumn/ps:Title) &gt; 0)"/>

					<xsl:variable name="isXSubcol" select="boolean(($xFormat='error' or count(ps:XColumn/ps:Subcolumn/ps:Title) &gt; 0) and not ($isXAdvancedColumn))"/>

					<xsl:variable name="isYSubcol" select="boolean(($yFormat='replicates'and ./@Replicates&gt;1) or 

						 ($yFormat!='text'and $yFormat!='replicates') or 

						count(ps:YColumn/ps:Subcolumn/ps:Title)&gt;0)"/>

					<xsl:variable name="isYSubColNameCustom" select="boolean($isYSubcol and count(ps:SubColumnTitles)&gt;0)"/>

					<TABLE BORDER="1" CELLSPACING="0">

						<TR>

							<xsl:for-each select="ps:RowTitlesColumn">

								<TD align="center">

									<xsl:attribute name="rowspan">

										<xsl:if test="$isXSubcol or $isXAdvancedSubcol or $isYSubcol">2</xsl:if>

									</xsl:attribute><BR/>

								</TD>

							</xsl:for-each>

							<xsl:if test="not ($isXAdvancedColumn)">

								<xsl:for-each select="ps:XColumn">

									<TD align="center">

										<xsl:attribute name="rowspan">

											<xsl:if test="$isYSubcol and not($isXSubcol)">2</xsl:if>

										</xsl:attribute>

										<xsl:attribute name="colspan"><xsl:value-of select="./@Subcolumns"/></xsl:attribute>

						 

						<xsl:apply-templates select="ps:Title"/>

										<xsl:if test="count(ps:Title)=0 or ps:Title=''">X-Title</xsl:if>				

						 

					</TD>

								</xsl:for-each>

							</xsl:if>

							<xsl:if test="$isXAdvancedColumn">

								<xsl:for-each select="ps:XAdvancedColumn">

									<TD align="center">

										<xsl:attribute name="rowspan">

											<xsl:if test="$isYSubcol and not($isXAdvancedSubcol)">2</xsl:if>

										</xsl:attribute>

										<xsl:attribute name="colspan"><xsl:value-of select="./@Subcolumns"/></xsl:attribute>

						 

						<xsl:apply-templates select="ps:Title"/>

										<xsl:if test="count(ps:Title)=0 or ps:Title=''">X-Title</xsl:if>				

						 

					</TD>

								</xsl:for-each>

							</xsl:if>

							<xsl:for-each select="ps:YColumn">

								<xsl:variable name="DefColName2" select="(position()-1) mod 26"/>

								<xsl:variable name="DefColName1" select="(position()-1-$DefColName2) div 26"/>

								<TD align="center">

									<xsl:attribute name="colspan"><xsl:value-of select="./@Subcolumns"/></xsl:attribute>

									<xsl:attribute name="rowspan">

										<xsl:if test="($isXSubcol or $isXAdvancedSubcol) and not($isYSubcol)">2</xsl:if>

									</xsl:attribute>

					 

					<xsl:apply-templates select="ps:Title"/>

									<xsl:if test="count(ps:Title)=0 or ps:Title=''">

										<xsl:value-of select="'Data Set-'"/>

										<xsl:if test="$DefColName1 &gt; 0">

											<xsl:value-of select="substring($Chars,$DefColName1,1)"/>

										</xsl:if>

										<xsl:value-of select="substring($Chars,$DefColName2+1,1)"/>

									</xsl:if>

					 				

	   			</TD>

							</xsl:for-each>

						</TR>

						<xsl:if test="$isXSubcol or $isXAdvancedSubcol or $isYSubcol">

							<TR>

								<xsl:if test="$isXSubcol">

									<xsl:for-each select="ps:XColumn">

										<xsl:for-each select="ps:Subcolumn">

											<TD align="center"> 

					 

					<xsl:apply-templates select="ps:Title"/>

												<xsl:if test="count(ps:Title)=0 or ps:Title=''">

													<xsl:choose>

														<xsl:when test="position()=1"><B>X</B></xsl:when>

														<xsl:when test="position()=2"><B>Err.Bar</B></xsl:when>

													</xsl:choose>

												</xsl:if>				

					 

				</TD>

										</xsl:for-each>

									</xsl:for-each>

								</xsl:if>

								<xsl:if test="$isXAdvancedSubcol">

									<xsl:for-each select="ps:XAdvancedColumn">

										<xsl:for-each select="ps:Subcolumn">

											<TD align="center"> 

					 

					<xsl:apply-templates select="ps:Title"/>

												<xsl:if test="count(ps:Title)=0 or ps:Title=''">

													<xsl:choose>

														<xsl:when test="position()=1"><B>Starting Date</B></xsl:when>

														<xsl:when test="position()=2"><B>Ending Date</B></xsl:when>

													</xsl:choose>

												</xsl:if>				

					 

				</TD>

										</xsl:for-each>

									</xsl:for-each>

								</xsl:if>

								<xsl:if test="$isYSubcol">

									<xsl:for-each select="ps:YColumn">

										<xsl:variable name="DefColName2" select="(position()-1) mod 26"/>

										<xsl:variable name="DefColName1" select="(position()-1-$DefColName2) div 26"/>

										<xsl:variable name="YColumnNdx" select="position()"/>

										<xsl:for-each select="ps:Subcolumn">

											<TD align="center">

					 

					<xsl:apply-templates select="ps:Title"/>

												<xsl:if test="count(ps:Title)=0 or ps:Title=''">

													<xsl:if test="$yFormat='replicates' or $yFormat='text'">

														<B>

															<xsl:if test="$DefColName1 &gt; 0">

																<xsl:value-of select="substring($Chars,$DefColName1,1)"/>

															</xsl:if>

															<xsl:choose>

																<xsl:when test="$isYSubColNameCustom">

																	<xsl:variable name="subColCustomNamePos" select="position()"/>

																	<xsl:for-each select="../../ps:SubColumnTitles/ps:Subcolumn">

																		<xsl:if test="position()=$subColCustomNamePos">

																			<xsl:choose>

																				<xsl:when test="../../ps:SubColumnTitles/@OwnSet='1'">

																					<xsl:variable name="DItemsCount" select="count(./ps:d)"/>

																					<xsl:choose>

																						<xsl:when test="$DItemsCount&lt;$YColumnNdx or ./ps:d[$YColumnNdx]=''">

																							<xsl:value-of select="substring($Chars,$DefColName2+1,1)"/>:Y<xsl:value-of select="position()"/>

																						</xsl:when>

																						<xsl:otherwise>

																							<xsl:apply-templates select="./ps:d[$YColumnNdx]"/>

																						</xsl:otherwise>

																					</xsl:choose>

																				</xsl:when>

																				<xsl:when test="../../ps:SubColumnTitles/@OwnSet='0'">

																					<xsl:variable name="DItemsCount" select="count(./ps:d)"/>

																					<xsl:choose>

																						<xsl:when test="$DItemsCount&lt;1 or ./ps:d[1]=''">

																							<xsl:value-of select="substring($Chars,$DefColName2+1,1)"/>:Y<xsl:value-of select="position()"/>

																						</xsl:when>

																						<xsl:otherwise>

																							<xsl:apply-templates select="./ps:d[1]"/>

																						</xsl:otherwise>

																					</xsl:choose>

																				</xsl:when>

																				<xsl:otherwise>

																					<xsl:apply-templates select="ps:d"/>

																					<xsl:if test="ps:d=''">

																						<xsl:value-of select="substring($Chars,$DefColName2+1,1)"/>:Y<xsl:value-of select="position()"/>

																					</xsl:if>

																				</xsl:otherwise>

																			</xsl:choose>

																		</xsl:if>

																	</xsl:for-each>

																</xsl:when>

																<xsl:otherwise>

																	<xsl:value-of select="substring($Chars,$DefColName2+1,1)"/>:Y<xsl:value-of select="position()"/>

																</xsl:otherwise>

															</xsl:choose>

														</B>

													</xsl:if>

													<xsl:if test="$yFormat!='replicates' and $yFormat!='text'">

														<xsl:if test="not($yFormat='replicates' or $yFormat='text')">

															<xsl:if test="position()=1"><B>Mean</B></xsl:if>

															<xsl:if test="position()!=1">

																<xsl:choose>

																	<xsl:when test="$yFormat='SD'">

																		<B>SD</B>

																	</xsl:when>

																	<xsl:when test="$yFormat='SE'">

																		<B>SEM</B>

																	</xsl:when>

																	<xsl:when test="$yFormat='CV'">

																		<B>%CV</B>

																	</xsl:when>

																	<xsl:when test="$yFormat='SDN'">

																		<xsl:if test="position()=2"><B>SD</B></xsl:if>

																		<xsl:if test="position()=3"><B>N</B></xsl:if>

																	</xsl:when>

																	<xsl:when test="$yFormat='SEN'">

																		<xsl:if test="position()=2"><B>SEM</B></xsl:if>

																		<xsl:if test="position()=3"><B>N</B></xsl:if>

																	</xsl:when>

																	<xsl:when test="$yFormat='CVN'">

																		<xsl:if test="position()=2"><B>%CV</B></xsl:if>

																		<xsl:if test="position()=3"><B>N</B></xsl:if>

																	</xsl:when>

																	<xsl:when test="$yFormat='low-high'">

																		<xsl:if test="position()=2"><B>+Error</B></xsl:if>

																		<xsl:if test="position()=3"><B>-Error</B></xsl:if>

																	</xsl:when>

																	<xsl:when test="$yFormat='upper-lower-limits'">

																		<xsl:if test="position()=2"><B>UpperLimit</B></xsl:if>

																		<xsl:if test="position()=3"><B>LowerLimit</B></xsl:if>

																	</xsl:when>

																</xsl:choose>

															</xsl:if>

														</xsl:if>

													</xsl:if>

												</xsl:if>

					 				

	   			</TD>

										</xsl:for-each>

									</xsl:for-each>

								</xsl:if>

							</TR>

						</xsl:if>

						<TR>

							<xsl:for-each select="ps:RowTitlesColumn/ps:Subcolumn">

								<td valign="top" nowrap="1">

									<xsl:attribute name="width"><xsl:value-of select="$width"/>%</xsl:attribute>

									<xsl:apply-templates select="ps:d"/>

									<xsl:if test="count(ps:d)=0"><br/></xsl:if>

								</td>

							</xsl:for-each>

							<xsl:if test="not($isXAdvancedColumn)">

								<xsl:for-each select="ps:XColumn/ps:Subcolumn">

									<td valign="top" nowrap="1">

										<xsl:attribute name="width"><xsl:value-of select="$width"/>%</xsl:attribute>

										<xsl:apply-templates select="ps:d"/>

										<xsl:if test="count(ps:d)=0"><br/></xsl:if>

									</td>

								</xsl:for-each>

							</xsl:if>

							<xsl:if test="$isXAdvancedColumn">

								<xsl:for-each select="ps:XAdvancedColumn/ps:Subcolumn">

									<td valign="top" nowrap="1">

										<xsl:attribute name="width"><xsl:value-of select="$width"/>%</xsl:attribute>

										<xsl:apply-templates select="ps:d"/>

										<xsl:if test="count(ps:d)=0"><br/></xsl:if>

									</td>

								</xsl:for-each>

							</xsl:if>

							<xsl:for-each select="ps:YColumn/ps:Subcolumn">

								<td valign="top" nowrap="1">

									<xsl:attribute name="width"><xsl:value-of select="$width"/>%</xsl:attribute>

									<xsl:apply-templates select="ps:d"/>

									<xsl:if test="count(ps:d)=0"><br/></xsl:if>

								</td>

							</xsl:for-each>

						</TR>

					</TABLE>

					<br/>

					<xsl:apply-templates select="ps:Info"/>

					<br/>

					<hr/>

					<br/>

				</xsl:for-each>

			</BODY>

		</HTML>

	</xsl:template>

	<!--<xsl:template match="ps:d">

	<xsl:choose>

		<xsl:when test="@Excluded and string-length(text())" >

		    <font color="#0000ee">

	      		<xsl:apply-templates select="node()"/>*		

		    </font>

		</xsl:when>

	    <xsl:otherwise>

			<xsl:apply-templates select="node()"/>

		</xsl:otherwise>

	</xsl:choose>

	<xsl:if test="position()!=last()"><br/></xsl:if>

</xsl:template>-->

	<xsl:template match="ps:d">

		<xsl:choose>

			<xsl:when test="@Excluded and string-length(text())">

				<xsl:choose>

					<xsl:when test="../../../@EVFormat='AsteriskAfterNumber'">

						<font color="#0000ee">

							<xsl:apply-templates select="node()"/>*

						</font>

					</xsl:when>

					<xsl:when test="../../../@EVFormat='Blank'">

					</xsl:when>

					<xsl:otherwise>

						<xsl:apply-templates select="node()"/>

					</xsl:otherwise>

				</xsl:choose>

			</xsl:when>

			<xsl:otherwise>

				<xsl:apply-templates select="node()"/>

			</xsl:otherwise>

		</xsl:choose>

		<xsl:if test="position()!=last()"><br/></xsl:if>

	</xsl:template>

	<xsl:template match="ps:Title">

		<b><xsl:apply-templates select="node()"/></b>

	</xsl:template>

	<xsl:template match="node()">

		<xsl:copy>

			<xsl:apply-templates select="node()"/>

		</xsl:copy>

	</xsl:template>

	<xsl:template match="ps:Font">

		<font>

			<xsl:if test="@Size">

				<xsl:attribute name="STYLE">font-size:<xsl:value-of select="@Size"/>pt</xsl:attribute>

			</xsl:if>

			<xsl:copy-of select="@*"/>

			<xsl:apply-templates select="node()"/>

		</font>

	</xsl:template>

	<xsl:template match="/">

		<HTML>

			<BODY>

				<xsl:apply-templates select="//ps:GraphPadPrismFile"/>

			</BODY>

		</HTML>

	</xsl:template>

</xsl:stylesheet>

