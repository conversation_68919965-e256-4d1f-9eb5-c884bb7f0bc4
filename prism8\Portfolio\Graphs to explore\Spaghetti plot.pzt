<?xml version="1.0" encoding="UTF-8"?>
<GraphPadPrismFile xmlns="http://graphpad.com/prism/Prism.htm" PrismXMLVersion="5.00">
<Created>
<OriginalVersion CreatedByProgram="GraphPad Prism" CreatedByVersion="6.0.0.206" Login="Geoff" DateTime="2011-09-19T15:58:06-08:00"/>
</Created>
<InfoSequence>
<Ref ID="Info0" Selected="1"/>
</InfoSequence>
<Info ID="Info0">
<Title>Project info 1</Title>
<Notes>
</Notes>
<Constant><Name>Experiment Date</Name><Value>2011-08-15</Value></Constant>
<Constant><Name>Experiment ID</Name><Value/></Constant>
<Constant><Name>Notebook ID</Name><Value/></Constant>
<Constant><Name>Project</Name><Value/></Constant>
<Constant><Name>Experimenter</Name><Value/></Constant>
<Constant><Name>Protocol</Name><Value/></Constant>
</Info>

<TableSequence>

<Ref ID="Table3"/>
<Ref ID="Table4" Selected="1"/>
</TableSequence>
<Table ID="Table3" XFormat="numbers" YFormat="replicates" Replicates="1" TableType="XY" EVFormat="AsteriskAfterNumber">
<Title>One subject per data set</Title>
<XColumn Width="81" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d>0</d>
<d>1</d>
<d>2</d>
<d>3</d>
<d>4</d>
<d>5</d>
<d>6</d>
<d>7</d>
<d>8</d>
<d>9</d>
<d>10</d>
<d>11</d>
</Subcolumn>
</XColumn>
<YColumn Width="81" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d>9.404157</d>
<d>10.65449</d>
<d>11.44737</d>
<d>10.75031</d>
<d>9.679844</d>
<d>9.890242</d>
<d>9.695828</d>
<d>8.187252</d>
<d>7.631425</d>
<d>7.313661</d>
<d>5.98579</d>
<d>4.54445</d>
</Subcolumn>
</YColumn>
<YColumn Width="81" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d>10.07814</d>
<d>9.897094</d>
<d>10.74494</d>
<d>11.65474</d>
<d>11.1119</d>
<d>9.97002</d>
<d>9.911846</d>
<d>9.565891</d>
<d>7.624594</d>
<d>7.238512</d>
<d>5.671759</d>
<d>4.91581</d>
</Subcolumn>
</YColumn>
<YColumn Width="81" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d>10.68659</d>
<d>11.17485</d>
<d>11.18192</d>
<d>10.86853</d>
<d>11.36817</d>
<d>11.61007</d>
<d>9.863927</d>
<d>9.50199</d>
<d>8.489107</d>
<d>7.67573</d>
<d>6.422674</d>
<d>5.411729</d>
</Subcolumn>
</YColumn>
<YColumn Width="81" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d>11.41528</d>
<d>12.03176</d>
<d>11.72682</d>
<d>12.59995</d>
<d>11.73938</d>
<d>11.42814</d>
<d>10.77214</d>
<d>10.66793</d>
<d>9.642642</d>
<d>8.605028</d>
<d>6.47994</d>
<d>7.013601</d>
</Subcolumn>
</YColumn>
<YColumn Width="81" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d>8.66637</d>
<d>9.070463</d>
<d>9.443017</d>
<d>10.16593</d>
<d>9.157967</d>
<d>8.285748</d>
<d>7.965854</d>
<d>7.809728</d>
<d>6.678405</d>
<d>6.154845</d>
<d>4.110161</d>
<d>3.676791</d>
</Subcolumn>
</YColumn>
<YColumn Width="81" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d>8.596437</d>
<d>9.157352</d>
<d>10.34318</d>
<d>9.333322</d>
<d>9.889812</d>
<d>8.825635</d>
<d>7.826658</d>
<d>7.610933</d>
<d>6.196028</d>
<d>5.74276</d>
<d>4.292043</d>
<d>3.150516</d>
</Subcolumn>
</YColumn>
<YColumn Width="81" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d>8.146038</d>
<d>7.879659</d>
<d>9.05514</d>
<d>9.165316</d>
<d>8.977503</d>
<d>8.885946</d>
<d>7.546012</d>
<d>7.606287</d>
<d>5.621609</d>
<d>5.465638</d>
<d>3.368868</d>
<d>3.060526</d>
</Subcolumn>
</YColumn>
<YColumn Width="81" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d>8.505774</d>
<d>8.762187</d>
<d>8.852065</d>
<d>8.922559</d>
<d>9.079907</d>
<d>8.461572</d>
<d>8.702033</d>
<d>6.660968</d>
<d>5.212871</d>
<d>5.312786</d>
<d>4.080206</d>
<d>2.839707</d>
</Subcolumn>
</YColumn>
</Table>
<Table ID="Table4" XFormat="numbers" YFormat="replicates" Replicates="8" TableType="XY" EVFormat="AsteriskAfterNumber">
<Title>One subject per subcolumn</Title>
<XColumn Width="81" Decimals="1" Subcolumns="1">
<Title/>
<Subcolumn>
<d>0</d>
<d>1</d>
<d>2</d>
<d>3</d>
<d>4</d>
<d>5</d>
<d>6</d>
<d>7</d>
<d>8</d>
<d>9</d>
<d>10</d>
<d>11</d>
</Subcolumn>
</XColumn>
<YColumn Width="648" Decimals="1" Subcolumns="8">
<Title/>
<Subcolumn>
<d>9.404157</d>
<d>10.65449</d>
<d>11.44737</d>
<d>10.75031</d>
<d>9.679844</d>
<d>9.890242</d>
<d>9.695828</d>
<d>8.187252</d>
<d>7.631425</d>
<d>7.313661</d>
<d>5.98579</d>
<d>4.54445</d>
</Subcolumn>
<Subcolumn>
<d>10.07814</d>
<d>9.897094</d>
<d>10.74494</d>
<d>11.65474</d>
<d>11.1119</d>
<d>9.97002</d>
<d>9.911846</d>
<d>9.565891</d>
<d>7.624594</d>
<d>7.238512</d>
<d>5.671759</d>
<d>4.91581</d>
</Subcolumn>
<Subcolumn>
<d>10.68659</d>
<d>11.17485</d>
<d>11.18192</d>
<d>10.86853</d>
<d>11.36817</d>
<d>11.61007</d>
<d>9.863927</d>
<d>9.50199</d>
<d>8.489107</d>
<d>7.67573</d>
<d>6.422674</d>
<d>5.411729</d>
</Subcolumn>
<Subcolumn>
<d>11.41528</d>
<d>12.03176</d>
<d>11.72682</d>
<d>12.59995</d>
<d>11.73938</d>
<d>11.42814</d>
<d>10.77214</d>
<d>10.66793</d>
<d>9.642642</d>
<d>8.605028</d>
<d>6.47994</d>
<d>7.013601</d>
</Subcolumn>
<Subcolumn>
<d>8.66637</d>
<d>9.070463</d>
<d>9.443017</d>
<d>10.16593</d>
<d>9.157967</d>
<d>8.285748</d>
<d>7.965854</d>
<d>7.809728</d>
<d>6.678405</d>
<d>6.154845</d>
<d>4.110161</d>
<d>3.676791</d>
</Subcolumn>
<Subcolumn>
<d>8.596437</d>
<d>9.157352</d>
<d>10.34318</d>
<d>9.333322</d>
<d>9.889812</d>
<d>8.825635</d>
<d>7.826658</d>
<d>7.610933</d>
<d>6.196028</d>
<d>5.74276</d>
<d>4.292043</d>
<d>3.150516</d>
</Subcolumn>
<Subcolumn>
<d>8.146038</d>
<d>7.879659</d>
<d>9.05514</d>
<d>9.165316</d>
<d>8.977503</d>
<d>8.885946</d>
<d>7.546012</d>
<d>7.606287</d>
<d>5.621609</d>
<d>5.465638</d>
<d>3.368868</d>
<d>3.060526</d>
</Subcolumn>
<Subcolumn>
<d>8.505774</d>
<d>8.762187</d>
<d>8.852065</d>
<d>8.922559</d>
<d>9.079907</d>
<d>8.461572</d>
<d>8.702033</d>
<d>6.660968</d>
<d>5.212871</d>
<d>5.312786</d>
<d>4.080206</d>
<d>2.839707</d>
</Subcolumn>
</YColumn>
</Table>

<!--Analyses, graphs and layouts as compressed binary. Don't edit this part of the file.-->

<Template xmlns:dt="urn:schemas-microsoft-com:datatypes" dt:dt="bin.base64">eNrsXQl8HFd5f7u6dqWV5DiyLZ8Z2zp8ypZjwISErGTHB9iOkJSQmAIZ7Y60E61m5NlZHQlJ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==</Template></GraphPadPrismFile>
